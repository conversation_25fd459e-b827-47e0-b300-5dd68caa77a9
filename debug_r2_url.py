import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'evelina'))

from modules.handlers.s3 import S3<PERSON><PERSON><PERSON>

async def debug_test():
    print("🔧 R2 URL Debug Test")
    print("=" * 50)
    
    # Initialize R2 client
    r2 = S3Handler(
        endpoint_url="https://fb78d0d6cda0aeb7bc82ee893af69b6e.r2.cloudflarestorage.com",
        aws_access_key_id="cd035a962c6d534d456619a613dd16d1",
        aws_secret_access_key="4721c751715211c8d24de5f7ca8bcd8919330c6cc3b7bec3d6e66bc81a404c63"
    )
    
    try:
        # Test 1: Upload to root of bucket (no directory)
        print("\n📤 Test 1: Upload to bucket root...")
        test_data = b"Hello from bucket root!"
        test_filename = "debug-root.txt"
        
        upload_result = await r2.upload_file("stun", test_data, test_filename, "text/plain")
        if upload_result.get('success'):
            print("✅ Root upload successful!")
            root_url = f"https://cdn.stun.lat/{test_filename}"
            print(f"🌐 Root URL: {root_url}")
        
        # Test 2: Upload to avatars directory
        print("\n📤 Test 2: Upload to avatars directory...")
        avatar_filename = "debug-avatar.txt"
        
        upload_result = await r2.upload_file("stun", test_data, avatar_filename, "text/plain", "avatars")
        if upload_result.get('success'):
            print("✅ Avatar directory upload successful!")
            avatar_url = f"https://cdn.stun.lat/avatars/{avatar_filename}"
            print(f"🌐 Avatar URL: {avatar_url}")
        
        # Test 3: Check what the actual bucket structure looks like
        print("\n🔍 Test 3: Checking file existence...")
        root_exists = await r2.file_exists("stun", test_filename)
        avatar_exists = await r2.file_exists("stun", avatar_filename, "avatars")
        print(f"Root file exists: {root_exists}")
        print(f"Avatar file exists: {avatar_exists}")
        
        print("\n📋 URLs to test in browser:")
        print(f"1. Root: {root_url}")
        print(f"2. Avatar: {avatar_url}")
        print(f"3. Public dev URL root: https://pub-fb78d0d6cda0aeb7bc82ee893af69b6e.r2.dev/{test_filename}")
        print(f"4. Public dev URL avatar: https://pub-fb78d0d6cda0aeb7bc82ee893af69b6e.r2.dev/avatars/{avatar_filename}")
        
        print("\n⚠️  Files left in bucket for testing - delete manually if needed")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_test())
