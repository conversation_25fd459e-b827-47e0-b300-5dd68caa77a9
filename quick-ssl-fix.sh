#!/bin/bash

# Quick fix to get SSL working
echo "🔧 Quick SSL setup fix..."

echo "📋 Step 1: Installing temporary HTTP-only configuration..."

# Install temporary config without SSL
sudo cp nginx-api-config-temp.conf /etc/nginx/sites-available/api.stun.lat

# Create web root for Let's Encrypt
sudo mkdir -p /var/www/html

# Test and reload
if sudo nginx -t; then
    echo "✅ Temporary config is valid"
    sudo systemctl reload nginx
    echo "🔄 Nginx reloaded"
else
    echo "❌ Still has errors"
    exit 1
fi

echo ""
echo "📋 Step 2: Now obtain SSL certificate..."
echo "Run this command:"
echo "   sudo certbot --nginx -d api.stun.lat"
echo ""
echo "📋 Step 3: After SSL certificate is obtained, run:"
echo "   sudo cp nginx-api-config.conf /etc/nginx/sites-available/api.stun.lat"
echo "   sudo nginx -t"
echo "   sudo systemctl reload nginx"
