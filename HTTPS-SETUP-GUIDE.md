# 🔒 HTTPS Setup Guide for api.stun.lat

## Overview
This guide will help you set up HTTPS for your API using Nginx as a reverse proxy with Let's Encrypt SSL certificates.

## Architecture
```
Internet → Nginx (Port 443 HTTPS) → Your Python API (Port 8080 HTTP)
```

## Prerequisites
- Domain `api.stun.lat` pointing to your server's IP address
- Root access to your server
- Your bot updated to use port 8080 (already done)

## Quick Setup (Automated)

1. **Make the setup script executable:**
   ```bash
   chmod +x setup-https.sh
   ```

2. **Run the setup script:**
   ```bash
   sudo ./setup-https.sh
   ```

## Manual Setup

### 1. Install Required Packages
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install nginx certbot python3-certbot-nginx -y
```

### 2. Configure Nginx
```bash
# Copy the configuration file
sudo cp nginx-api-config.conf /etc/nginx/sites-available/api.stun.lat

# Enable the site
sudo ln -s /etc/nginx/sites-available/api.stun.lat /etc/nginx/sites-enabled/

# Remove default site (optional)
sudo rm /etc/nginx/sites-enabled/default

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

### 3. Obtain SSL Certificate
```bash
# Make sure api.stun.lat points to your server first!
sudo certbot --nginx -d api.stun.lat
```

### 4. Enable Services
```bash
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 5. Set Up Auto-Renewal
```bash
# Add to crontab for automatic certificate renewal
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
```

## Testing

### 1. Restart Your Bot
After making these changes, restart your bot so it uses port 8080.

### 2. Test Endpoints
```bash
# Test health endpoint
curl https://api.stun.lat/health

# Test exempt endpoints (no API key needed)
curl https://api.stun.lat/commands
curl https://api.stun.lat/shards

# Test protected endpoints (API key required)
curl "https://api.stun.lat/fun/cat?key=X3pZmLq82VnHYTd6Cr9eAw"
```

### 3. Test Bot Commands
- `/botinfo` - Should fetch data from https://api.stun.lat/health
- `/shards` - Should fetch data from https://api.stun.lat/health

## Troubleshooting

### Common Issues

1. **"Connection refused" errors:**
   - Make sure your bot is running and listening on port 8080
   - Check: `netstat -tlnp | grep 8080`

2. **SSL certificate errors:**
   - Ensure `api.stun.lat` DNS points to your server
   - Check: `dig api.stun.lat`

3. **Nginx errors:**
   - Check logs: `sudo tail -f /var/log/nginx/error.log`
   - Test config: `sudo nginx -t`

4. **Bot can't reach API:**
   - Make sure the bot can resolve `api.stun.lat`
   - Test from bot server: `curl https://api.stun.lat/health`

### Useful Commands

```bash
# Check Nginx status
sudo systemctl status nginx

# Check SSL certificate
sudo certbot certificates

# Reload Nginx configuration
sudo systemctl reload nginx

# Check what's listening on ports
sudo netstat -tlnp | grep -E ':(80|443|8080)'

# Test SSL configuration
curl -I https://api.stun.lat/health
```

## Security Features

The Nginx configuration includes:
- ✅ HTTP to HTTPS redirect
- ✅ Modern SSL/TLS protocols only
- ✅ Security headers (HSTS, X-Frame-Options, etc.)
- ✅ Rate limiting (10 requests/second with burst)
- ✅ Proper proxy headers

## What Changed

1. **API Server:** Now runs on `127.0.0.1:8080` (localhost only)
2. **Bot Commands:** Now fetch from `https://api.stun.lat/health`
3. **Nginx:** Handles HTTPS termination and proxies to your app
4. **SSL:** Let's Encrypt certificate for `api.stun.lat`

## Next Steps

After setup is complete:
1. ✅ Update any other services that call your API to use HTTPS
2. ✅ Monitor SSL certificate expiration (auto-renewal is configured)
3. ✅ Consider adding monitoring for your API endpoints
4. ✅ Test all API endpoints to ensure they work with HTTPS
