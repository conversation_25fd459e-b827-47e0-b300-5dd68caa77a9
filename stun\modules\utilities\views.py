import json
from io import BytesIO
from typing import Any, List, Optional, Union

import discord
from discord import ui
from discord.ui import View, Button, button
import re


class VoiceRename(discord.ui.Modal, title="Rename your voice channel"):
    name = discord.ui.TextInput(
        label="Name",
        style=discord.TextStyle.short,
        placeholder="The voice channel's new name",
        max_length=32,
        min_length=2,
        custom_id="vm:modal",
    )

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.user.voice.channel.edit(name=self.name.value)
        return await interaction.response.send_message(
            f"Renamed your voice channel to **{self.name.value}**", ephemeral=True
        )

    async def on_error(self, interaction: discord.Interaction, _):
        return await interaction.response.send_message(
            "An error occured while trying to edit your voice channel's name",
            ephemeral=True,
        )


class Giveaway(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(
        emoji="🎉",
        custom_id="giveaway:join",
        style=discord.ButtonStyle.primary,
        row=0
    )
    async def giveaway_join(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Get giveaway data
        giveaway = await interaction.client.db.fetchrow("""
            SELECT * FROM giveaway.giveaways
            WHERE message_id = $1
        """, interaction.message.id)

        if not giveaway:
            return await interaction.response.send_message(
                "This giveaway no longer exists!", ephemeral=True
            )

        # Check if already entered
        existing = await interaction.client.db.fetchrow("""
            SELECT * FROM giveaway.entries 
            WHERE message_id = $1 AND user_id = $2
        """, interaction.message.id, interaction.user.id)

        if existing:
            class LeaveButton(discord.ui.Button):
                def __init__(self):
                    super().__init__(
                        label="Leave Giveaway",
                        emoji="🚪",
                        style=discord.ButtonStyle.danger,
                        custom_id="giveaway:leave"
                    )

                async def callback(self, leave_interaction: discord.Interaction):
                    # Remove entry
                    await interaction.client.db.execute("""
                        DELETE FROM giveaway.entries 
                        WHERE message_id = $1 AND user_id = $2
                    """, interaction.message.id, interaction.user.id)

                    # Update entry count
                    total_entries = await interaction.client.db.fetchval("""
                        SELECT COUNT(*) FROM giveaway.entries WHERE message_id = $1
                    """, interaction.message.id)

                    # Update giveaway entry count
                    await interaction.client.db.execute("""
                        UPDATE giveaway.giveaways 
                        SET entry_count = $1
                        WHERE message_id = $2
                    """, total_entries, interaction.message.id)

                    # Update main giveaway message
                    await interaction.message.edit(embed=await self.create_giveaway_embed(interaction, giveaway))

                    # Send confirmation
                    await leave_interaction.response.send_message(
                        "You have left the giveaway!", ephemeral=True
                    )

            # Create leave view
            leave_view = discord.ui.View(timeout=None)
            leave_view.add_item(LeaveButton())

            # Create embed for leave confirmation
            embed = discord.Embed(
                description="You are already in this giveaway",
                color=0x2b2d31
            )

            return await interaction.response.send_message(
                embed=embed,
                view=leave_view,
                ephemeral=True
            )

        member = interaction.guild.get_member(interaction.user.id)
        if not member:
            return

        # Check requirements
        failed_requirements = []

        # Required roles
        if giveaway['required_roles']:
            for role_id in giveaway['required_roles']:
                role = interaction.guild.get_role(role_id)
                if role and role not in member.roles:
                    failed_requirements.append(f"Required Role: {role.mention}")

        # Blacklisted roles
        if giveaway['blacklisted_roles']:
            for role_id in giveaway['blacklisted_roles']:
                role = interaction.guild.get_role(role_id)
                if role and role in member.roles:
                    failed_requirements.append(f"Blacklisted Role: {role.mention}")

        if failed_requirements:
            embed = discord.Embed(
                title="Entry Denied",
                description="You don't meet the requirements:\n" + "\n".join(failed_requirements),
                color=0xff0000
            )
            return await interaction.response.send_message(embed=embed, ephemeral=True)

        # Calculate entries based on multiplier roles
        entries = 1
        if giveaway['multiplier_roles']:
            multipliers = giveaway['multiplier_roles']
            for role in member.roles:
                if str(role.id) in multipliers:
                    entries = max(entries, float(multipliers[str(role.id)]))

        # Add entry
        await interaction.client.db.execute("""
            INSERT INTO giveaway.entries (message_id, user_id, entries)
            VALUES ($1, $2, $3)
        """, interaction.message.id, interaction.user.id, entries)

        # Update entry count
        total_entries = await interaction.client.db.fetchval("""
            SELECT COUNT(*) FROM giveaway.entries WHERE message_id = $1
        """, interaction.message.id)

        # Update giveaway entry count
        await interaction.client.db.execute("""
            UPDATE giveaway.giveaways 
            SET entry_count = $1
            WHERE message_id = $2
        """, total_entries, interaction.message.id)

        # Update giveaway message
        await interaction.message.edit(embed=await self.create_giveaway_embed(interaction, giveaway))

        # Send success message
        embed = discord.Embed(
            description=f"You joined the giveaway with {entries:g} entries! 🎉",
            color=0x2b2d31
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    @discord.ui.button(
        label="Participants",
        emoji="📋",
        custom_id="giveaway:participants",
        style=discord.ButtonStyle.secondary,
        row=0
    )
    async def view_participants(self, interaction: discord.Interaction, button: discord.ui.Button):
        entries = await interaction.client.db.fetch("""
            SELECT user_id, entries FROM giveaway.entries 
            WHERE message_id = $1 
            ORDER BY entry_time ASC
        """, interaction.message.id)

        if not entries:
            description = "No participants yet"
        else:
            description = "\n".join(
                f"`{i+1}.` {interaction.guild.get_member(entry['user_id']).mention} ({entry['entries']:g} entries)"
                for i, entry in enumerate(entries)
            )

        embed = discord.Embed(
            title=f"Participants ({len(entries)})",
            description=description,
            color=0x2b2d31
        )
        await interaction.response.send_message(embed=embed, ephemeral=True)

    async def create_giveaway_embed(self, interaction: discord.Interaction, giveaway: dict) -> discord.Embed:
        entries = await interaction.client.db.fetch("""
            SELECT user_id, entries FROM giveaway.entries 
            WHERE message_id = $1
        """, interaction.message.id)

        total_entries = sum(entry['entries'] for entry in entries)
        host = interaction.guild.get_member(giveaway['host_id'])
        
        embed = discord.Embed(
            title=giveaway['prize'],
            color=0x2b2d31
        )
        
        embed.add_field(
            name="Ends",
            value=f"<t:{int(giveaway['end_time'].timestamp())}:R>",
            inline=True
        )
        
        embed.add_field(
            name="Hosted by",
            value=f"@{host.name}" if host else "Unknown",
            inline=True
        )
        
        embed.add_field(
            name="Winners",
            value=str(giveaway['winner_count']),
            inline=True
        )

        if total_entries > 0:
            embed.add_field(
                name="Entries",
                value=str(len(entries)),
                inline=True
            )

            if giveaway['required_roles']:
                roles = []
                for role_id in giveaway['required_roles']:
                    if role := interaction.guild.get_role(role_id):
                        roles.append(role.mention)
                if roles:
                    embed.add_field(
                        name="Role",
                        value="\n".join(roles),
                        inline=True
                    )

        return embed


# tanks guys for da plural class :3
class plural:
    def __init__(
        self: "plural",
        value: Union[int, str, List[Any]],
        number: bool = True,
        md: str = "",
    ):
        self.value: int = (
            len(value)
            if isinstance(value, list)
            else (
                (
                    int(value.split(" ", 1)[-1])
                    if value.startswith(("CREATE", "DELETE"))
                    else int(value)
                )
                if isinstance(value, str)
                else value
            )
        )
        self.number: bool = number
        self.md: str = md

    def __format__(self: "plural", format_spec: str) -> str:
        v = self.value
        singular, sep, plural = format_spec.partition("|")
        plural = plural or f"{singular}s"
        result = f"{self.md}{v:,}{self.md} " if self.number else ""

        result += plural if abs(v) != 1 else singular
        return result


def shorten(value: str, length: int = 24) -> str:
    if len(value) > length:
        value = value[: length - 2] + (".." if len(value) > length else "").strip()

    return value


def format_duration(value: int, ms: bool = True) -> str:
    h = int((value / (1000 * 60 * 60)) % 24) if ms else int((value / (60 * 60)) % 24)
    m = int((value / (1000 * 60)) % 60) if ms else int((value / 60) % 60)
    s = int((value / 1000) % 60) if ms else int(value % 60)

    result = ""
    if h:
        result += f"{h}:"

    result += "00:" if not m else f"{m}:"
    result += "00" if not s else f"{str(s).zfill(2)}"

    return result


async def setup(bot):
    pass
