import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'evelina'))

from modules.handlers.s3 import S3<PERSON><PERSON><PERSON>

async def test_public_access():
    print("🔧 Testing R2 Public Access")
    print("=" * 50)
    
    # Initialize R2 client (uses config settings)
    r2 = S3Handler()
    
    try:
        # Upload a simple test file
        print("\n📤 Uploading test file...")
        test_data = b"Hello World! This is a public access test."
        test_filename = "public-test.txt"
        
        upload_result = await r2.upload_file("stun", test_data, test_filename, "text/plain")
        if upload_result.get('success'):
            print("✅ Upload successful!")
            
            # Test different URL patterns
            urls_to_test = [
                f"https://cdn.stun.lat/{test_filename}",
                f"https://pub-fb78d0d6cda0aeb7bc82ee893af69b6e.r2.dev/{test_filename}",
                f"https://fb78d0d6cda0aeb7bc82ee893af69b6e.r2.cloudflarestorage.com/stun/{test_filename}",
            ]
            
            print("\n🌐 URLs to test:")
            for i, url in enumerate(urls_to_test, 1):
                print(f"{i}. {url}")
            
            print("\n📋 Try these URLs in your browser:")
            print("If ANY of them work, we can configure the bot to use that pattern.")
            
            # Don't delete the file so you can test the URLs
            print(f"\n⚠️  File '{test_filename}' left in bucket for testing")
            print("Delete it manually after testing if needed")
            
        else:
            print(f"❌ Upload failed: {upload_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_public_access())
