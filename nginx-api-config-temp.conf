# Temporary Nginx configuration for HTTPS API (before SSL certificate)
# This will be used to obtain the SSL certificate, then replaced with the full config

server {
    listen 80;
    server_name api.stun.lat;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Rate limiting (using zone defined in main nginx.conf)
    limit_req zone=api burst=20 nodelay;
    
    # API endpoints - proxy to your Python app on port 8080
    location / {
        # Proxy settings
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
    }
    
    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # Logging
    access_log /var/log/nginx/api.stun.lat.access.log;
    error_log /var/log/nginx/api.stun.lat.error.log;
}
