[{"name": "level", "description": "Setup the leveling module", "permissions": "N/A", "aliases": [], "category": "leveling", "arguments": "member"}, {"name": "level multiplier", "description": "Set the multiplier for the leveling system", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "multiplier"}, {"name": "level voice", "description": "Manage voice mulipliers for leveling system", "permissions": "N/A", "aliases": ["manage guild"], "category": "leveling", "arguments": "N/A"}, {"name": "level voice booster", "description": "Set the voice multiplier for boosters", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "multiplier"}, {"name": "level voice multiplier", "description": "Set the voice multiplier for the leveling system", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "multiplier"}, {"name": "level voice rolemultiplier", "description": "Manage the role voice multipliers", "permissions": "manage guild", "aliases": ["r<PERSON><PERSON>i", "rmultiplier"], "category": "leveling", "arguments": "N/A"}, {"name": "level voice rolemultiplier add", "description": "Add or update a role voice multiplier", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "role, multiplier"}, {"name": "level voice rolemultiplier remove", "description": "Remove a role voice multiplier", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "role"}, {"name": "level multipliers", "description": "Displays the current multipliers for the server", "permissions": "N/A", "aliases": ["multis"], "category": "leveling", "arguments": "N/A"}, {"name": "level ignore", "description": "Manage the XP ignore list", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level ignore remove", "description": "Remove a user, role or channel from the XP ignore list", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "target"}, {"name": "level ignore command", "description": "Enable or disable the option to exclude commands from the leveling system", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "option"}, {"name": "level ignore add", "description": "Add a user, role or channel to the XP ignore list", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "target"}, {"name": "level ignore list", "description": "View the XP ignore list", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level leaderboard", "description": "View the highest-ranking members", "permissions": "N/A", "aliases": ["lb"], "category": "leveling", "arguments": "N/A"}, {"name": "level test", "description": "View the level up message for the server", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level rolemultiplier", "description": "Manage the role multipliers", "permissions": "N/A", "aliases": ["r<PERSON><PERSON>i", "rmultiplier"], "category": "leveling", "arguments": "N/A"}, {"name": "level rolemultiplier add", "description": "Add or update a role multiplier", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "role, multiplier"}, {"name": "level rolemultiplier remove", "description": "Remove a role multiplier", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "role"}, {"name": "level enable", "description": "Enable the leveling system", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level set", "description": "Set a user's level", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "member, level"}, {"name": "level message", "description": "Set a custom level up message", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "message"}, {"name": "level rewards", "description": "Manage the rewards for leveling up", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level rewards list", "description": "Get a list of every role reward in this server", "permissions": "N/A", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level rewards sync", "description": "Sync all user's roles based on their levels", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level rewards reset", "description": "Remove every reward that was added", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level rewards stack", "description": "Toggle stacking rewards for leveling up", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "option"}, {"name": "level rewards add", "description": "Assign a reward role to a level", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "level, role"}, {"name": "level rewards remove", "description": "Remove a reward from a level", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "role"}, {"name": "level disable", "description": "Disable the leveling system", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "N/A"}, {"name": "level reset", "description": "Reset a user's level and XP", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "member"}, {"name": "level booster", "description": "Set the multiplier for boosters", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "multiplier"}, {"name": "level config", "description": "Check the settings for the leveling system", "permissions": "manage guild", "aliases": ["settings", "stats", "statistics", "status"], "category": "leveling", "arguments": "N/A"}, {"name": "level channel", "description": "Set a channel for the level up messages", "permissions": "manage guild", "aliases": [], "category": "leveling", "arguments": "channel"}, {"name": "voice", "description": "Manage your VoiceMaster channel", "permissions": "N/A", "aliases": ["vc"], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice transfer", "description": "Transfer ownership of your voice channel to another member", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "member"}, {"name": "voice rename", "description": "Rename your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice preset", "description": "Manage your voice channel presets", "permissions": "N/A", "aliases": ["p"], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice preset load", "description": "Apply a saved preset to the current temp voice channel", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice preset delete", "description": "Delete a voicemaster preset", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice preset list", "description": "List all presets created by the user", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice preset autoload", "description": "Automatically load the preset of the user when they join a voice channel", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice preset update", "description": "Update an existing preset with the current temp voice channel's settings.", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice preset edit", "description": "Add/remove a user or role to the overwrites of a preset", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name, target"}, {"name": "voice preset add", "description": "Create a preset for the current temp voice channel", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "voice claim", "description": "Claim an inactive voice channel", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice reveal", "description": "Reveal your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice reject", "description": "Reject a member or role from joining your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "member"}, {"name": "voice status", "description": "Chance the status of your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "status"}, {"name": "voice hide", "description": "Hide your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice lock", "description": "Lock your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice kick", "description": "Kick a member or role from joining your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "member"}, {"name": "voice unlock", "description": "Unlock your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voice limit", "description": "Change the user limit of your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "limit"}, {"name": "voice permit", "description": "Permit a member or role to join your voice channel", "permissions": "voice owner", "aliases": [], "category": "voicemaster", "arguments": "member"}, {"name": "resolved", "description": "Mark a thread as resolved", "permissions": "N/A", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "remind", "description": "Add a reminder", "permissions": "N/A", "aliases": ["remindme"], "category": "utility", "arguments": "time, task"}, {"name": "rank", "description": "View your level and experience", "permissions": "N/A", "aliases": [], "category": "leveling", "arguments": "member"}, {"name": "serverbanner", "description": "N/A", "permissions": "N/A", "aliases": ["sbanner"], "category": "utility", "arguments": "member"}, {"name": "networth", "description": "Check your networth", "permissions": "N/A", "aliases": ["nw"], "category": "economy", "arguments": "user"}, {"name": "gunslist", "description": "List all linked guns.lol accounts", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "N/A"}, {"name": "autobanner", "description": "Automatically send banners to a channel", "permissions": "N/A", "aliases": [], "category": "autopost", "arguments": "N/A"}, {"name": "autobanner remove", "description": "Remove an autobanner channel", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "N/A"}, {"name": "autobanner name", "description": "Change the way how the bot webhook is named", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "name"}, {"name": "autobanner avatar", "description": "Change the way how the bot webhook is avatar", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "avatar"}, {"name": "autobanner add", "description": "Add an autobanner channel", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "channel"}, {"name": "autorole", "description": "Set up automatic role assign on member join", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole bots", "description": "Set up automatic role assign for bots", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole bots add", "description": "Adds an autorole for bots and assigns on join", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole bots clear", "description": "Clears every autorole for bots in guild", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole bots list", "description": "View a list of every auto role for bots", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole bots remove", "description": "Removes an autorole for bots", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole humans", "description": "Set up automatic role assign for humans", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole humans add", "description": "Adds an autorole for humans and assigns on join", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole humans list", "description": "View a list of every auto role for humans", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole humans remove", "description": "Removes an autorole for humans", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole humans clear", "description": "Clears every autorole for humans in guild", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole all", "description": "Set up automatic role assign for all members", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole all remove", "description": "Removes an autorole for all members", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole all list", "description": "View a list of every auto role for all members", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "autorole all add", "description": "Adds an autorole for all members and assigns on join", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "autorole all clear", "description": "Clears every autorole for all members in guild", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "voicemaster", "description": "Make temporary voice channels in your server", "permissions": "N/A", "aliases": ["vm"], "category": "voicemaster", "arguments": "N/A"}, {"name": "voicemaster blacklist", "description": "Blacklist a word that can no longer be used as a voicemaster name", "permissions": "manage guild", "aliases": [], "category": "voicemaster", "arguments": "words"}, {"name": "voicemaster setup", "description": "Start the setup for the VoiceMaster module", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voicemaster bitrate", "description": "Set the bitrate for the voice channels created by the bot", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "bitrate"}, {"name": "voicemaster unblacklist", "description": "Unblacklist a word to allow it as a voicemaster name again", "permissions": "manage guild", "aliases": [], "category": "voicemaster", "arguments": "words"}, {"name": "voicemaster region", "description": "Set the region for the voice channels created by the bot", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "region"}, {"name": "voicemaster category", "description": "Set the category for the voice channels created by the bot", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "category"}, {"name": "voicemaster blacklisted", "description": "Shows all blacklisted words for voicemaster names", "permissions": "manage guild", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voicemaster savesettings", "description": "Enable or disable if user settings should be saved", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "option"}, {"name": "voicemaster unsetup", "description": "Reset server configuration for VoiceMaster", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "N/A"}, {"name": "voicemaster name", "description": "Set the name for the voice channels created by the bot", "permissions": "N/A", "aliases": [], "category": "voicemaster", "arguments": "name"}, {"name": "apply", "description": "Apply for an open application", "permissions": "N/A", "aliases": [], "category": "application", "arguments": "N/A"}, {"name": "spotify", "description": "Spotify Integration Commands", "permissions": "N/A", "aliases": ["sp"], "category": "spotify", "arguments": "N/A"}, {"name": "spotify playlists", "description": "Displays all Spotify playlists of the user", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify pause", "description": "Pause the current playback on Spotify if it is not already paused", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify login", "description": "Login to your Spotify account", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify logout", "description": "Logout from Spotify your account", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify back", "description": "Go back to the previous track", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify playlist", "description": "Plays a specific Spotify playlist by name", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "name"}, {"name": "spotify devices", "description": "Shows available devices for Spotify playback with a dropdown menu", "permissions": "N/A", "aliases": ["device"], "category": "spotify", "arguments": "N/A"}, {"name": "spotify play", "description": "Start playing a track on Spotify", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "track"}, {"name": "spotify current", "description": "Displays the currently playing track on Spotify", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify skip", "description": "Skip to the next track", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify resume", "description": "Resume playback on Spotify if it is currently paused", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "N/A"}, {"name": "spotify volume", "description": "Check or set the volume on the selected Spotify device", "permissions": "N/A", "aliases": ["vol"], "category": "spotify", "arguments": "volume"}, {"name": "spotify info", "description": "Displays information about the connected Spotify account and active device.", "permissions": "N/A", "aliases": [], "category": "spotify", "arguments": "user"}, {"name": "beta", "description": "Beta commands", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "beta command", "description": "Beta command commands", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "beta command add", "description": "Add a command to the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "command"}, {"name": "beta command list", "description": "List all commands in the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "beta command remove", "description": "Remove a command from the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "command"}, {"name": "beta remove", "description": "Remove a user from the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user"}, {"name": "beta list", "description": "List all users in the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "beta add", "description": "Add a user to the beta program", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user"}, {"name": "donators", "description": "Returns a list of all donators", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "names", "description": "View username and nickname history of a member or yourself", "permissions": "N/A", "aliases": ["pastusernames", "usernames", "oldnames", "pastnames"], "category": "utility", "arguments": "user"}, {"name": "imagecount", "description": "Get the count of how much images are available for every category", "permissions": "N/A", "aliases": [], "category": "autopost", "arguments": "N/A"}, {"name": "guildnames", "description": "View guildname history", "permissions": "N/A", "aliases": ["gnames"], "category": "utility", "arguments": "guild"}, {"name": "clearnames", "description": "Reset your name history", "permissions": "N/A", "aliases": ["cnames"], "category": "utility", "arguments": "N/A"}, {"name": "ownerhistory", "description": "View the owner history of a guild", "permissions": "N/A", "aliases": ["oh"], "category": "utility", "arguments": "N/A"}, {"name": "leave", "description": "Set up a goodbye message in one or multiple channels", "permissions": "N/A", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "leave remove", "description": "Remove a goodbye message from a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "leave list", "description": "View goodbye message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "leave reset", "description": "Remove all the leave messages", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "leave test", "description": "Test the leave message in a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "leave add", "description": "Add a goodbye message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel, code"}, {"name": "clearguildnames", "description": "Reset guild name history", "permissions": "N/A", "aliases": ["<PERSON><PERSON><PERSON>"], "category": "utility", "arguments": "N/A"}, {"name": "clearownerhistory", "description": "Reset the owner history of a guild", "permissions": "N/A", "aliases": ["clearowner"], "category": "utility", "arguments": "N/A"}, {"name": "roleinfo", "description": "View information about a role", "permissions": "N/A", "aliases": ["ri"], "category": "utility", "arguments": "role"}, {"name": "invoke", "description": "Change punishment messages for DM or command response", "permissions": "N/A", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message", "description": "Change punishment messages for command response", "permissions": "N/A", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jail", "description": "Change jail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jail add", "description": "Add jail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message jail view", "description": "View jail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jail remove", "description": "Remove jail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jail test", "description": "Test jail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unmute", "description": "Change unmute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unmute test", "description": "Test unmute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unmute add", "description": "Add unmute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message unmute view", "description": "View unmute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unmute remove", "description": "Remove unmute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message mute", "description": "Change mute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message mute remove", "description": "Remove mute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message mute view", "description": "View mute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message mute add", "description": "Add mute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message mute test", "description": "Test mute message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message kick", "description": "Change kick message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message kick add", "description": "Add kick message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message kick view", "description": "View kick message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message kick remove", "description": "Remove kick message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message kick test", "description": "Test kick message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unban", "description": "Change unban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unban remove", "description": "Remove unban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unban test", "description": "Test unban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unban add", "description": "Add unban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message unban view", "description": "View unban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message ban", "description": "Change ban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message ban view", "description": "View ban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message ban remove", "description": "Remove ban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message ban test", "description": "Test ban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message ban add", "description": "Add ban message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message warn", "description": "Change warn message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message warn test", "description": "Test warn message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message warn add", "description": "Add warn message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message warn view", "description": "View warn message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message warn remove", "description": "Remove warn message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unjail", "description": "Change unjail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unjail add", "description": "Add unjail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message unjail view", "description": "View unjail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unjail remove", "description": "Remove unjail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message unjail test", "description": "Test unjail message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jailchannel", "description": "Change jail channel message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jailchannel remove", "description": "Remove jail channel message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jailchannel view", "description": "View jail channel message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke message jailchannel add", "description": "Add jail channel message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke message jailchannel test", "description": "Test jail channel message for command response", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm", "description": "Change punishment messages for DM", "permissions": "N/A", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm ban", "description": "Change ban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm ban add", "description": "Add ban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm ban view", "description": "View ban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm ban remove", "description": "Remove ban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm ban test", "description": "Test ban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm warn", "description": "Change warn message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm warn add", "description": "Add warn message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm warn test", "description": "Test warn message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm warn view", "description": "View warn message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm warn remove", "description": "Remove warn message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unjail", "description": "Change unjail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unjail test", "description": "Test unjail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unjail add", "description": "Add unjail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm unjail view", "description": "View unjail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unjail remove", "description": "Remove unjail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unmute", "description": "Change unmute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unmute view", "description": "View unmute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unmute add", "description": "Add unmute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm unmute remove", "description": "Remove unmute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unmute test", "description": "Test unmute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm jail", "description": "Change jail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm jail view", "description": "View jail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm jail remove", "description": "Remove jail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm jail test", "description": "Test jail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm jail add", "description": "Add jail message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm mute", "description": "Change mute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm mute test", "description": "Test mute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm mute remove", "description": "Remove mute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm mute view", "description": "View mute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm mute add", "description": "Add mute message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm kick", "description": "Change kick message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm kick test", "description": "Test kick message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm kick add", "description": "Add kick message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "invoke dm kick view", "description": "View kick message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm kick remove", "description": "Remove kick message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unban", "description": "Change unban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unban view", "description": "View unban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unban remove", "description": "Remove unban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unban test", "description": "Test unban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "N/A"}, {"name": "invoke dm unban add", "description": "Add unban message for DM", "permissions": "manage guild", "aliases": [], "category": "invoke", "arguments": "code"}, {"name": "permissions", "description": "View permissions of a member or role", "permissions": "N/A", "aliases": ["perms"], "category": "utility", "arguments": "target"}, {"name": "checkout", "description": "Create a checkout for a user", "permissions": "N/A", "aliases": ["co"], "category": "reseller", "arguments": "user, amount"}, {"name": "channelinfo", "description": "View information about a channel", "permissions": "N/A", "aliases": ["ci"], "category": "utility", "arguments": "channel"}, {"name": "transparent", "description": "Remove background from an image", "permissions": "N/A", "aliases": ["tp"], "category": "utility", "arguments": "url"}, {"name": "guild", "description": "View assets from your a given guild", "permissions": "N/A", "aliases": ["server"], "category": "utility", "arguments": "N/A"}, {"name": "guild banner", "description": "Returns server banner", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "invite"}, {"name": "guild icon", "description": "Returns server icon", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "invite"}, {"name": "guild splash", "description": "Returns server splash background", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "invite"}, {"name": "emoji", "description": "Manage the server's emojis", "permissions": "N/A", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "emoji rename", "description": "<PERSON><PERSON> an emoji", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "emoji, name"}, {"name": "emoji add", "description": "Add an emoji to the server", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "emoji addmulti", "description": "Add multiple emojis at the same time", "permissions": "manage expressions", "aliases": ["am"], "category": "emoji", "arguments": "emojis"}, {"name": "emoji remove", "description": "Remove an emoji from the server", "permissions": "manage expressions", "aliases": ["delete", "del"], "category": "emoji", "arguments": "emoji"}, {"name": "emoji list", "description": "Returns a list of emojis in this server", "permissions": "N/A", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "emoji info", "description": "Information about an emoji", "permissions": "N/A", "aliases": [], "category": "emoji", "arguments": "emoji"}, {"name": "emoji steal", "description": "Steal an emoji from another server", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "emoji, name"}, {"name": "emoji zip", "description": "Send a zip file of all emojis in the server", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "emoji enlarge", "description": "Gets an image version of your emoji", "permissions": "N/A", "aliases": ["download", "e", "jumbo"], "category": "emoji", "arguments": "emoji"}, {"name": "emoji search", "description": "Search emojis based by query", "permissions": "N/A", "aliases": [], "category": "emoji", "arguments": "query"}, {"name": "emoji sticker", "description": "Convert a custom emoji to a sticker", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "emoji, name"}, {"name": "snipe", "description": "Snipe the latest message that was deleted", "permissions": "N/A", "aliases": ["s"], "category": "utility", "arguments": "N/A"}, {"name": "reactionsnipe", "description": "Snipe the latest reaction that was removed", "permissions": "N/A", "aliases": ["rs"], "category": "utility", "arguments": "N/A"}, {"name": "color", "description": "Show a hex codes color in a embed", "permissions": "N/A", "aliases": ["colour"], "category": "utility", "arguments": "color"}, {"name": "editsnipe", "description": "Snipe the latest message that was edited", "permissions": "N/A", "aliases": ["es"], "category": "utility", "arguments": "N/A"}, {"name": "compress", "description": "Compress or resize an image or GIF based on percentage", "permissions": "N/A", "aliases": ["resize"], "category": "utility", "arguments": "percentage, url"}, {"name": "spotify_old", "description": "Get information about spotify", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "spotify_old track", "description": "Search for a track on spotify", "permissions": "N/A", "aliases": ["tr"], "category": "utility", "arguments": "query"}, {"name": "clearsnipes", "description": "Clear the snipes from the channel.", "permissions": "Manage messages", "aliases": ["cs"], "category": "utility", "arguments": "N/A"}, {"name": "warns", "description": "Shows all warns of an user", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "membercount", "description": "View server member count", "permissions": "N/A", "aliases": ["mc"], "category": "utility", "arguments": "invite"}, {"name": "nuke", "description": "Replace the current channel with a new one", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "nuke remove", "description": "Remove a nuke schedule for a channel.", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "nuke add", "description": "Add a nuke schedule for a channel.", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "channel, schedule"}, {"name": "nuke list", "description": "List all nuke schedules for the current server with the next nuke time.", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "crypto", "description": "Convert cryptocurrency to a specified currency", "permissions": "N/A", "aliases": ["convert", "conv"], "category": "utility", "arguments": "amount, from_currency, to_currency"}, {"name": "serverinfo", "description": "View information about a server", "permissions": "N/A", "aliases": ["si"], "category": "utility", "arguments": "server"}, {"name": "movie", "description": "Get information about a movie", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "query"}, {"name": "userinfo", "description": "View information about a member or yourself", "permissions": "N/A", "aliases": ["user", "ui", "whois"], "category": "utility", "arguments": "member"}, {"name": "boost", "description": "Set up a boost message in one or multiple channels", "permissions": "N/A", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "boost list", "description": "View boost message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "boost reset", "description": "Remove all the boost messages", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "boost test", "description": "Test the boost message in a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "boost add", "description": "Add a boost message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel, code"}, {"name": "boost remove", "description": "Remove a boost message from a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "calculate", "description": "Calculate a mathematical expression including percentages", "permissions": "N/A", "aliases": ["calc", "math"], "category": "utility", "arguments": "expression"}, {"name": "info", "description": "View information about a user", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "member"}, {"name": "ocr", "description": "Perform OCR on an image or an attached image", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "image"}, {"name": "devices", "description": "Send what device you or another person is using", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "member"}, {"name": "lyrics", "description": "Get the lyrics for a song", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "song"}, {"name": "weather", "description": "Gets simple weather from OpenWeatherMap", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "city"}, {"name": "messages", "description": "Shows messages statistics", "permissions": "N/A", "aliases": ["m", "msg"], "category": "utility", "arguments": "user"}, {"name": "messages total", "description": "Shows messages sent for the total of the server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "messages user", "description": "Shows messages sent by a user", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "user"}, {"name": "messages weekly", "description": "Shows messages sent in the past 7 days", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "messages global", "description": "Shows total messages sent across all servers", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "messages daily", "description": "Shows messages sent in the past 24 hours", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "messages monthly", "description": "Shows messages sent in the past 30 days", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "channel", "description": "Manage channels in your sever", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "channel topic", "description": "Change a channel's topic", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "channel, topic"}, {"name": "channel create", "description": "Create a channel in your server", "permissions": "manage channels", "aliases": ["make"], "category": "moderation", "arguments": "name"}, {"name": "channel rename", "description": "Rename a channel", "permissions": "manage channels", "aliases": ["name"], "category": "moderation", "arguments": "channel, name"}, {"name": "channel nsfw", "description": "Toggle NSFW for a channel", "permissions": "manage channels", "aliases": ["naughty"], "category": "moderation", "arguments": "channel"}, {"name": "channel category", "description": "Move a channel to a new category", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "channel, category"}, {"name": "channel remove", "description": "Delete a channel in your server", "permissions": "manage channels", "aliases": ["delete", "del"], "category": "moderation", "arguments": "channel"}, {"name": "autoresponder", "description": "Set up automatic replies to messages that match a trigger", "permissions": "N/A", "aliases": ["ar"], "category": "responders", "arguments": "N/A"}, {"name": "autoresponder allow", "description": "Set specific users, channels, or roles as allowed for a trigger", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "trigger, target"}, {"name": "autoresponder remove", "description": "Remove a reply for a trigger word", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "trigger"}, {"name": "autoresponder deny", "description": "Set specific users, channels, or roles as denied for a trigger", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "trigger, target"}, {"name": "autoresponder clear", "description": "Remove all autoresponders", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoresponder add", "description": "Create a reply for a trigger word", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "response"}, {"name": "autoresponder list", "description": "View a list of auto-reply triggers in guild", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoresponder permissions", "description": "Shows the allowed and denied users, roles, and channels for all autoresponder triggers", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "twitter", "description": "Gets profile information on the given Twitter user", "permissions": "N/A", "aliases": ["x"], "category": "twitter", "arguments": "username"}, {"name": "twitter add", "description": "Create feed for new tweets from a user", "permissions": "manage server", "aliases": [], "category": "twitter", "arguments": "username, channel, message"}, {"name": "twitter remove", "description": "Remove feed for new tweets", "permissions": "manage server", "aliases": [], "category": "twitter", "arguments": "username"}, {"name": "twitter list", "description": "View list of every Twitter feed", "permissions": "manage server", "aliases": [], "category": "twitter", "arguments": "N/A"}, {"name": "twitter check", "description": "Get the latest tweet from a user", "permissions": "N/A", "aliases": [], "category": "twitter", "arguments": "username"}, {"name": "perks", "description": "Check the perks that you get for donating $3 to us / boost our server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "vote", "description": "Command that sends the vote link for top.gg", "permissions": "N/A", "aliases": [], "category": "vote", "arguments": "N/A"}, {"name": "vote remind", "description": "Remind the user to vote for <PERSON><PERSON>", "permissions": "N/A", "aliases": [], "category": "vote", "arguments": "N/A"}, {"name": "vote add", "description": "Add a vote to a user", "permissions": "N/A", "aliases": [], "category": "vote", "arguments": "user"}, {"name": "vote leaderboard", "description": "Leaderboard for top.gg votes", "permissions": "N/A", "aliases": [], "category": "vote", "arguments": "N/A"}, {"name": "vote claim", "description": "Claim the vote reward", "permissions": "N/A", "aliases": [], "category": "vote", "arguments": "N/A"}, {"name": "steal", "description": "Steal an emoji from another server", "permissions": "manage expressions", "aliases": ["add"], "category": "emoji", "arguments": "emoji, name"}, {"name": "urban", "description": "Gets the definition of a word/slang from Urban Dictionary", "permissions": "N/A", "aliases": ["define"], "category": "utility", "arguments": "word"}, {"name": "seen", "description": "Check when a user was last seen", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "user"}, {"name": "visible", "description": "N/A", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "starboard", "description": "Showcase the best messages in your server", "permissions": "N/A", "aliases": [], "category": "starboard", "arguments": "N/A"}, {"name": "starboard reset", "description": "Resets guild's configuration for starboard", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "N/A"}, {"name": "starboard ignore", "description": "Ignore channels or users from starboard", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "N/A"}, {"name": "starboard ignore add", "description": "Add a channel or user to the ignore list", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "target"}, {"name": "starboard ignore remove", "description": "Remove a channel or user from the ignore list", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "target"}, {"name": "starboard enable", "description": "Enable the suggestion module", "permissions": "manage guild", "aliases": ["on"], "category": "starboard", "arguments": "N/A"}, {"name": "starboard count", "description": "Sets the default amount stars needed to post", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "count"}, {"name": "starboard disable", "description": "Disable the suggestion module", "permissions": "manage guild", "aliases": ["off"], "category": "starboard", "arguments": "N/A"}, {"name": "starboard channel", "description": "Sets the channel where starboard messages will be sent to", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "channel"}, {"name": "starboard emoji", "description": "Sets the emoji that triggers the starboard messages", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "emoji"}, {"name": "starboard config", "description": "View the settings for starboard in guild", "permissions": "manage guild", "aliases": [], "category": "starboard", "arguments": "N/A"}, {"name": "pull", "description": "Pull the latest changes from the repository", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "joindm", "description": "Set up a join DM message", "permissions": "N/A", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "joindm test", "description": "Test the join DM message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "joindm add", "description": "Add a join DM message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "code"}, {"name": "joindm reset", "description": "Remove the join DM message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "joindm remove", "description": "Remove the join DM message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "joindm list", "description": "View the join DM message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "translate", "description": "Translate a message to a specific language", "permissions": "N/A", "aliases": ["tr"], "category": "utility", "arguments": "language, message"}, {"name": "restart", "description": "N/A", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "option"}, {"name": "afk", "description": "Set an AFK status for when you are mentioned", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "reason"}, {"name": "dominant", "description": "Grab the most dominant color from an image, emoji, or URL", "permissions": "N/A", "aliases": ["hex"], "category": "utility", "arguments": "input"}, {"name": "youngest", "description": "Get the youngest account in the server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "oldest", "description": "Get the oldest account in the server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "timer", "description": "Post repeating messages in your server", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "timer list", "description": "View all auto messages in your server", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "timer remove", "description": "Remove repeating message from a channel", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "channel"}, {"name": "timer test", "description": "Preview a channel's auto message", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "channel"}, {"name": "timer add", "description": "Add repeating message to a channel", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "channel, interval, code"}, {"name": "feedback", "description": "Send feedback about the bot", "permissions": "administrator", "aliases": [], "category": "utility", "arguments": "feedback"}, {"name": "picperms", "description": "Give a member permissions to post attachments in a channel", "permissions": "manage messages", "aliases": ["pic"], "category": "utility", "arguments": "member, channel"}, {"name": "category", "description": "Manage categories in your server", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "category rename", "description": "Rename a category in your server", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "category, name"}, {"name": "category duplicate", "description": "Clone an already existing category in your server", "permissions": "N/A", "aliases": ["clone", "remake"], "category": "moderation", "arguments": "category"}, {"name": "category create", "description": "Create a category in your server", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "name"}, {"name": "category delete", "description": "Delete a category in your server", "permissions": "manage channels", "aliases": [], "category": "moderation", "arguments": "category"}, {"name": "roles", "description": "View all roles in the server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "enlarge", "description": "Get an image version of an emoji", "permissions": "N/A", "aliases": ["e", "jumbo"], "category": "emoji", "arguments": "emoji"}, {"name": "stickerperms", "description": "Give a member permissions to use stickers in a channel", "permissions": "manage messages", "aliases": [], "category": "utility", "arguments": "member, channel"}, {"name": "forward", "description": "Forward a message to a channel", "permissions": "manage messages", "aliases": ["fw"], "category": "utility", "arguments": "channel"}, {"name": "emojiperms", "description": "Give a member permissions to use emojis in a channel", "permissions": "manage messages", "aliases": [], "category": "utility", "arguments": "member, channel"}, {"name": "whitelist", "description": "Manage the whitelist module", "permissions": "administrator", "aliases": ["wl"], "category": "whitelist", "arguments": "N/A"}, {"name": "whitelist message", "description": "Change the message sent to users when not in the whitelist", "permissions": "administrator", "aliases": ["msg", "dm"], "category": "whitelist", "arguments": "code"}, {"name": "whitelist enable", "description": "Turn on the whitelist system", "permissions": "administrator", "aliases": [], "category": "whitelist", "arguments": "N/A"}, {"name": "whitelist add", "description": "Add someone to the server whitelist", "permissions": "administrator", "aliases": [], "category": "whitelist", "arguments": "user"}, {"name": "whitelist punishment", "description": "Change the punishment for not being whitelisted", "permissions": "administrator", "aliases": ["punish"], "category": "whitelist", "arguments": "punishment"}, {"name": "whitelist disable", "description": "Turn off the whitelist system", "permissions": "administrator", "aliases": [], "category": "whitelist", "arguments": "N/A"}, {"name": "whitelist remove", "description": "Remove someone from the server whitelist", "permissions": "administrator", "aliases": [], "category": "whitelist", "arguments": "user"}, {"name": "whitelist list", "description": "View all whitelisted members", "permissions": "administrator", "aliases": [], "category": "whitelist", "arguments": "N/A"}, {"name": "welcome", "description": "Set up a welcome message in one or multiple channels", "permissions": "N/A", "aliases": ["greet", "wlc", "welc"], "category": "events", "arguments": "N/A"}, {"name": "welcome list", "description": "View all welcome messages", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "welcome remove", "description": "Remove a welcome message from a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "welcome add", "description": "Add a welcome message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel, code"}, {"name": "welcome joinping", "description": "Add a join ping in a specific channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "welcome delete", "description": "Delete a welcome message", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "welcome delete enable", "description": "Enable the welcome message deletion", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel, time"}, {"name": "welcome delete disable", "description": "Disable the welcome message deletion", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "welcome test", "description": "View welcome message for a channel", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "channel"}, {"name": "welcome reset", "description": "Remove all the welcome messages", "permissions": "manage guild", "aliases": [], "category": "events", "arguments": "N/A"}, {"name": "sync", "description": "Sync all relevant roles for a user on <PERSON><PERSON>'s server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "user"}, {"name": "activity", "description": "Shows activity statistics", "permissions": "N/A", "aliases": ["act"], "category": "utility", "arguments": "user, range"}, {"name": "activity voice", "description": "Shows activity statistics for voice with an optional time range", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "range"}, {"name": "activity user", "description": "Shows activity statistics for a user with an optional time range", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "user, range"}, {"name": "activity channel", "description": "Shows activity statistics for a channel with an optional time range", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "channel, range"}, {"name": "activity leaderboard", "description": "Shows the activity leaderboard for the server", "permissions": "N/A", "aliases": ["lb"], "category": "utility", "arguments": "N/A"}, {"name": "activity leaderboard remove", "description": "Remove the activity leaderboard for a channel", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "table, range"}, {"name": "activity leaderboard list", "description": "List all activity leaderboards for the server", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "activity leaderboard add", "description": "Add activity leaderboard for a channel", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "channel, table, range"}, {"name": "activity server", "description": "Shows activity statistics for the server with an optional time range", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "range"}, {"name": "activity messages", "description": "Shows activity statistics for messages with an optional time range", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "range"}, {"name": "activity ignore", "description": "Manage ignored channels for activity tracking", "permissions": "N/A", "aliases": ["ig"], "category": "utility", "arguments": "N/A"}, {"name": "activity ignore add", "description": "Add a channel to the ignored list for activity tracking", "permissions": "manage guild", "aliases": ["a"], "category": "utility", "arguments": "channel"}, {"name": "activity ignore list", "description": "List all ignored channels for activity tracking", "permissions": "manage guild", "aliases": ["l"], "category": "utility", "arguments": "N/A"}, {"name": "activity ignore remove", "description": "Remove a channel from the ignored list for activity tracking", "permissions": "manage guild", "aliases": ["r"], "category": "utility", "arguments": "channel"}, {"name": "guilds", "description": "All guilds the bot is in, sorted from the biggest to the smallest", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "muted", "description": "Returns a list of muted members whose mute is still active", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "joins", "description": "View members who joined the server within a given timeframe", "permissions": "N/A", "aliases": ["joined"], "category": "utility", "arguments": "timeframe"}, {"name": "counter", "description": "Create counters for everybody to see", "permissions": "N/A", "aliases": [], "category": "counters", "arguments": "N/A"}, {"name": "counter remove", "description": "Remove a counter from the server", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "countertype, role"}, {"name": "counter list", "description": "Returns a list of the active server counters", "permissions": "N/A", "aliases": [], "category": "counters", "arguments": "N/A"}, {"name": "counter types", "description": "Returns the counter types and channel types", "permissions": "N/A", "aliases": [], "category": "counters", "arguments": "N/A"}, {"name": "counter add", "description": "Create channel counter", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "N/A"}, {"name": "counter add humans", "description": "Add a counter for non bots members", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "counter add boosts", "description": "Add a counter for boosts", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "counter add bots", "description": "Add a counter for bots", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "counter add role", "description": "Add a counter for a specific role", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, role, message"}, {"name": "counter add voice", "description": "Add a counter for members that are connected to a voice channel", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "counter add boosters", "description": "Add a counter for boosters", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "counter add members", "description": "Add a counter for the member count", "permissions": "manage guild", "aliases": [], "category": "counters", "arguments": "channeltype, message"}, {"name": "leaves", "description": "View members who left the server within a given timeframe", "permissions": "N/A", "aliases": ["left"], "category": "utility", "arguments": "timeframe"}, {"name": "voicerole", "description": "Set up voice channel role assign", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "voicerole list", "description": "Lists all voice channels and their assigned roles", "permissions": "Lists all voice channels and their assigned roles", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "voicerole remove", "description": "Removes a voice channel role assign", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "channel, role"}, {"name": "voicerole default", "description": "Set up default role for voice channel role assign", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "voicerole default add", "description": "Sets a default role for voice channel role assign", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "role"}, {"name": "voicerole default remove", "description": "Removes the default role for voice channel role assign", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "voicerole add", "description": "Adds a voice channel role assign", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "channel, role"}, {"name": "voicerole clear", "description": "Clears every voice channel role assign in guild", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "sticker", "description": "Manage server's stickers", "permissions": "N/A", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "sticker tag", "description": "Add your server's vanity URL to the end of sticker names", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "sticker steal", "description": "Steal a sticker from another server", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "sticker add", "description": "Add a sticker with an image attachment", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "sticker zip", "description": "Send a zip file containing the server's stickers", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "N/A"}, {"name": "sticker rename", "description": "Rename a sticker", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "sticker delete", "description": "Delete a sticker", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "sticker enlarge", "description": "Returns a sticker as a file", "permissions": "N/A", "aliases": ["e", "jumbo"], "category": "emoji", "arguments": "N/A"}, {"name": "sticker emoji", "description": "Convert a sticker to a custom emoji and optionally attach it to a message", "permissions": "manage expressions", "aliases": [], "category": "emoji", "arguments": "name"}, {"name": "colorrole", "description": "Colorrole commands", "permissions": "N/A", "aliases": ["cr"], "category": "colorrole", "arguments": "N/A"}, {"name": "colorrole set", "description": "Set or change your color role", "permissions": "N/A", "aliases": [], "category": "colorrole", "arguments": "color"}, {"name": "colorrole setup", "description": "Enable Colorrole system in your server", "permissions": "manage guild", "aliases": [], "category": "colorrole", "arguments": "N/A"}, {"name": "colorrole remove", "description": "Remove your current color role", "permissions": "N/A", "aliases": [], "category": "colorrole", "arguments": "N/A"}, {"name": "colorrole base", "description": "Set the base role for where boost roles will go under", "permissions": "manage guild", "aliases": [], "category": "colorrole", "arguments": "role"}, {"name": "colorrole reset", "description": "Disable Colorrole system in your server", "permissions": "manage guild", "aliases": [], "category": "colorrole", "arguments": "N/A"}, {"name": "bans", "description": "Returns a list of banned users", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "bots", "description": "Returns a list of all bots in this server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "boosters", "description": "View all recent server boosters", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "boosterslost", "description": "View all recent server boosters that unboosted", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "timezone", "description": "View your current time or somebody elses", "permissions": "N/A", "aliases": ["tz"], "category": "utility", "arguments": "member"}, {"name": "timezone set", "description": "Set your timezone", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "timezone"}, {"name": "timezone unset", "description": "Unset your timezone", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "timezone check", "description": "Check the current time for a specific location", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "location"}, {"name": "timezone list", "description": "View a list of every member's timezone", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "staff", "description": "Manage the staff team.", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "staff remove", "description": "Remove a user from the staff team", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user"}, {"name": "staff social", "description": "Manage the social media profiles of the staff team.", "permissions": "bot helper", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "staff social edit", "description": "Edit a social media profile of yourself", "permissions": "bot helper", "aliases": [], "category": "developer", "arguments": "platform, link"}, {"name": "staff social remove", "description": "Remove a social media profile from yourself", "permissions": "bot helper", "aliases": [], "category": "developer", "arguments": "platform"}, {"name": "staff social list", "description": "List all social media profiles of yourself", "permissions": "bot helper", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "staff social forceedit", "description": "Edit a social media profile of a staff member", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user, platform, link"}, {"name": "staff social forcelist", "description": "List all social media profiles of a staff member", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user"}, {"name": "staff social forceadd", "description": "Add a social media profile to a staff member", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user, platform, link"}, {"name": "staff social add", "description": "Add a social media profile to yourself", "permissions": "bot helper", "aliases": [], "category": "developer", "arguments": "platform, link"}, {"name": "staff social forceremove", "description": "Remove a social media profile from a staff member", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user, platform"}, {"name": "staff edit", "description": "Edit the rank of a staff member", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user, rank"}, {"name": "staff add", "description": "Add a user to the staff team", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user, rank"}, {"name": "inrole", "description": "View members in a role", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "role"}, {"name": "installs", "description": "List all installs", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "pin", "description": "Pin a message", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "message"}, {"name": "invites", "description": "Returns the number of invites you have in the server", "permissions": "N/A", "aliases": ["invitetracker", "invt", "it"], "category": "invitetracker", "arguments": "member"}, {"name": "invites fake-threshold", "description": "Set the fake invite threshold for the server.", "permissions": "manage guild", "aliases": ["ft", "threshold"], "category": "invitetracker", "arguments": "threshold"}, {"name": "invites logs", "description": "Set the channel for the invite logs.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "channel"}, {"name": "invites enable", "description": "Enable invite tracking for the server.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites bonus", "description": "Manage the bonus invites for the server.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites bonus remove", "description": "Remove bonus invites from a user.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "member, bonus"}, {"name": "invites bonus add", "description": "Add bonus invites to a user.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "member, bonus"}, {"name": "invites invited", "description": "Returns a list where all users are listed who got invited by the provided user", "permissions": "N/A", "aliases": [], "category": "invitetracker", "arguments": "member"}, {"name": "invites code", "description": "Display all of your or a user's invite codes.", "permissions": "N/A", "aliases": [], "category": "invitetracker", "arguments": "member"}, {"name": "invites inviter", "description": "Show who invited the user to the server.", "permissions": "N/A", "aliases": ["invited-by"], "category": "invitetracker", "arguments": "member"}, {"name": "invites disable", "description": "Disable the invite tracker", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites message", "description": "Set the message for the invite tracker.", "permissions": "manage guild", "aliases": ["msg"], "category": "invitetracker", "arguments": "message"}, {"name": "invites leaderboard", "description": "Show the invite leaderboard for the server.", "permissions": "N/A", "aliases": ["lb"], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites rewards", "description": "Manage the invite rewards for the server.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites rewards stack", "description": "Toggle the removal of old invite rewards when a new one is added", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "option"}, {"name": "invites rewards list", "description": "List all the role rewards for the invite tracker.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites rewards remove", "description": "Remove a role reward for the invite tracker.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "threshold"}, {"name": "invites rewards sync", "description": "Sync the role rewards with the current invites.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "N/A"}, {"name": "invites rewards add", "description": "Add a role reward for the invite tracker.", "permissions": "manage guild", "aliases": [], "category": "invitetracker", "arguments": "threshold, role"}, {"name": "shazam", "description": "Find a song by providing video or audio", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "thread", "description": "Manage threads", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "thread create", "description": "Create a thread in a channel", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "message, name"}, {"name": "thread watch", "description": "Watch a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "mode"}, {"name": "thread auto", "description": "Manage auto-thread settings", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "thread auto list", "description": "List all auto-thread channels", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "thread auto add", "description": "Add a channel to auto-thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "thread auto remove", "description": "Remove a channel from auto-thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "thread delete", "description": "Delete a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "thread"}, {"name": "thread add", "description": "Add a member to a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "thread lock", "description": "Lock a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "thread"}, {"name": "thread unlock", "description": "Unlock a locked thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "thread"}, {"name": "thread remove", "description": "Remove a member from a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "globalban", "description": "Ban an user globally", "permissions": "bot developer", "aliases": ["gban"], "category": "developer", "arguments": "user, reason"}, {"name": "buttonmessage", "description": "Buttonmessage commands", "permissions": "N/A", "aliases": ["buttonmsg"], "category": "responders", "arguments": "N/A"}, {"name": "buttonmessage remove", "description": "Remove a specific button from a message by its custom_id", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "button_id"}, {"name": "buttonmessage edit", "description": "Edit a button's embed response", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "button_id, embed"}, {"name": "buttonmessage list", "description": "View a list of every button role", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "buttonmessage add", "description": "Add a button to a message\n> If you don't want to use an emoji/label, just type `none`", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "message, label, emoji, color, embed"}, {"name": "buttonmessage clear", "description": "Remove all buttons from a message", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "message"}, {"name": "unpin", "description": "Unpin a message", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "message"}, {"name": "boosterrole", "description": "Boosterrole Commands", "permissions": "N/A", "aliases": ["br"], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole list", "description": "View all booster roles", "permissions": "N/A", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole name", "description": "Edit your booster roles name", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "name"}, {"name": "boosterrole base", "description": "Set the base role for where boost roles will go under", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "role"}, {"name": "boosterrole delete", "description": "Delete your booster role", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole setup", "description": "Enable Boosterrole system in your server", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole blacklist", "description": "Manage the blacklist for boosterrole names/users", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole blacklist word", "description": "Blacklist a word that can no longer be used as a Boosterrole name", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "words"}, {"name": "boosterrole blacklist user", "description": "Blacklist a member from using boosterroles", "permissions": "Manage guild", "aliases": [], "category": "boosterrole", "arguments": "user, reason"}, {"name": "boosterrole color", "description": "Edit your booster roles color", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "color"}, {"name": "boosterrole blacklisted", "description": "List all blacklisted words for boosterrole names/users", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole blacklisted user", "description": "List all blacklisted users from using boosterroles", "permissions": "Manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole blacklisted word", "description": "Shows all blacklisted words for boosterrole names", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole wipe", "description": "Wipe a member's booster role", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "user"}, {"name": "boosterrole allow", "description": "Allow roles to create booster roles", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole allow add", "description": "Allow a role to create booster roles", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "role"}, {"name": "boosterrole allow remove", "description": "Remove a role from creating booster roles", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "role"}, {"name": "boosterrole allow list", "description": "List all roles that can create booster roles", "permissions": "N/A", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole award", "description": "Give additional roles to members when they boost the server", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole award add", "description": "Reward a member a specific role upon boost", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "role"}, {"name": "boosterrole award list", "description": "Returns all the booster role awards in this server", "permissions": "N/A", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole award remove", "description": "Remove the reward role", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "role"}, {"name": "boosterrole unblacklist", "description": "Manage the unblacklist for boosterrole names/users", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole unblacklist user", "description": "Unblacklist a member from using boosterroles", "permissions": "Manage guild", "aliases": [], "category": "boosterrole", "arguments": "user"}, {"name": "boosterrole unblacklist word", "description": "Unblacklist a word to allow it as a boosterrole name again", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "words"}, {"name": "boosterrole limit", "description": "Set the limit of booster roles that can be created", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "limit"}, {"name": "boosterrole icon", "description": "Edit your booster roles icon", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "icon"}, {"name": "boosterrole share", "description": "Share your booster role with others", "permissions": "N/A", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole share list", "description": "List all members you are sharing your booster role with", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole share limit", "description": "Limit the amount of people who can share your booster role", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "limit"}, {"name": "boosterrole share enable", "description": "Enable sharing your booster role", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole share add", "description": "Add a member to share your booster role", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "user"}, {"name": "boosterrole share disable", "description": "Disable sharing your booster role", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole share remove", "description": "Remove a member from sharing your booster role", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "user"}, {"name": "boosterrole reset", "description": "Disable Boosterrole system in your server", "permissions": "manage guild", "aliases": [], "category": "boosterrole", "arguments": "N/A"}, {"name": "boosterrole create", "description": "Create a booster role", "permissions": "server booster", "aliases": [], "category": "boosterrole", "arguments": "color, name"}, {"name": "appinfo", "description": "Get the bot's application info", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "antiraid", "description": "Configure protection against potential raids", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "<PERSON>raid unwhitelist", "description": "Unwhitelist a user or role from Antiraid system", "permissions": "administrator", "aliases": ["uwl"], "category": "antiraid", "arguments": "user"}, {"name": "antiraid config", "description": "View current antiraid configuration", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid whitelisted", "description": "View all current antiraid whitelists", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid setup", "description": "Setup antiraid in your server", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid logs", "description": "Set logs channel for antiraid", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "channel"}, {"name": "antiraid avatar", "description": "Punish members with default avatars", "permissions": "N/A", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid avatar enable", "description": "Enable default avatar protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "punishment"}, {"name": "antiraid avatar disable", "description": "Disable default avatar protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid avatar punishment", "description": "Change punishment for default avatar protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "punishment"}, {"name": "antiraid whitelist", "description": "Create a one-time whitelist to allow a user to join", "permissions": "administrator", "aliases": ["wl"], "category": "antiraid", "arguments": "user"}, {"name": "antiraid age", "description": "Punish new registered accounts", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid age punishment", "description": "Change punishment for new accounts protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "punishment"}, {"name": "antiraid age enable", "description": "Enable new accounts protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "time, punishment"}, {"name": "antiraid age disable", "description": "Disable new accounts protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid massjoin", "description": "Prevent join raids on your server", "permissions": "N/A", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid massjoin punishment", "description": "Change punishment for anti mass join protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "punishment"}, {"name": "antiraid massjoin disable", "description": "Disbale antiraid mass join protection", "permissions": "administrator", "aliases": ["dis"], "category": "antiraid", "arguments": "N/A"}, {"name": "antiraid massjoin threshold", "description": "Change threshold for mass join protection", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "threshold"}, {"name": "antiraid massjoin enable", "description": "Enable mass join protection", "permissions": "administrator", "aliases": ["e"], "category": "antiraid", "arguments": "punishment, threshold"}, {"name": "antiraid reset", "description": "Reset antiraid in your server", "permissions": "administrator", "aliases": [], "category": "antiraid", "arguments": "N/A"}, {"name": "twitch", "description": "Twitch command group", "permissions": "manage server", "aliases": [], "category": "twitch", "arguments": "N/A"}, {"name": "twitch add", "description": "Add stream notifications to channel", "permissions": "manage server", "aliases": [], "category": "twitch", "arguments": "streamer, channel, message"}, {"name": "twitch check", "description": "Check if a streamer is currently live", "permissions": "N/A", "aliases": [], "category": "twitch", "arguments": "streamer"}, {"name": "twitch remove", "description": "Remove stream notifications from a channel", "permissions": "manage server", "aliases": [], "category": "twitch", "arguments": "streamer"}, {"name": "twitch list", "description": "View all Twitch stream notifications", "permissions": "manage server", "aliases": [], "category": "twitch", "arguments": "N/A"}, {"name": "stat", "description": "Shows the amount of lines, functions, classes, imports, and files the bot has.", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "creator", "description": "Manage users permissions to post embeds on <PERSON><PERSON>'s page", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "creator add", "description": "Add a user premissions to post embeds", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user"}, {"name": "creator remove", "description": "Remove a user premissions to post embeds", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user"}, {"name": "creator list", "description": "List all premitted users that can post embeds", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "status", "description": "N/A", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "birthday", "description": "View your birthday or somebody else's", "permissions": "N/A", "aliases": ["bday"], "category": "utility", "arguments": "user"}, {"name": "birthday globallist", "description": "View a list of every user's birthday", "permissions": "N/A", "aliases": ["glist"], "category": "utility", "arguments": "N/A"}, {"name": "birthday reward", "description": "Set a reward for your birthday", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "birthday reward unset", "description": "Unset your birthday reward", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "birthday reward set", "description": "Set a role as a reward for your birthday", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "role"}, {"name": "birthday reward view", "description": "View your birthday reward", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "birthday set", "description": "Set your birthday", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "date"}, {"name": "birthday list", "description": "View a list of every member's birthday in this server", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "birthday unset", "description": "Unset your birthday", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "looplatency", "description": "Returns the latency of the main event loop", "permissions": "bot developer", "aliases": ["mel", "ll"], "category": "developer", "arguments": "N/A"}, {"name": "checkperms", "description": "N/A", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "avatarhistory", "description": "N/A", "permissions": "N/A", "aliases": ["avh"], "category": "utility", "arguments": "user"}, {"name": "avatarhistory clear", "description": "Clear your avatar history", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "avatarhistory enable", "description": "Enable avatar history tracking", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "avatarhistory disable", "description": "Disable avatar history tracking", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "avatarhistory view", "description": "Check a member's avatar history", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "user"}, {"name": "avatarhistory global", "description": "Returns a list of all avatar histories", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "checknames", "description": "Check if the bot has a custom nickname on any server", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "N/A"}, {"name": "uwu<PERSON>", "description": "Convert a message to the uwu format", "permissions": "N/A", "aliases": ["uwu"], "category": "utility", "arguments": "message"}, {"name": "termed", "description": "Transfer data from old user to new user", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "old, new"}, {"name": "tc", "description": "Close the current ticket", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "wipe", "description": "Wipe an user's data", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user"}, {"name": "quest", "description": "Manage your quests", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "quest stop", "description": "Stop your quest", "permissions": "N/A", "aliases": ["delete"], "category": "economy", "arguments": "N/A"}, {"name": "quest leaderboard", "description": "View the quest leaderboard", "permissions": "N/A", "aliases": ["lb"], "category": "economy", "arguments": "N/A"}, {"name": "quest start", "description": "Start a quest", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "difficulty"}, {"name": "quest complete", "description": "Complete your quest", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "quest status", "description": "View the status of your quest", "permissions": "N/A", "aliases": ["info"], "category": "economy", "arguments": "N/A"}, {"name": "storagevault", "description": "Manage your Storagevault account", "permissions": "N/A", "aliases": ["sv"], "category": "storagevault", "arguments": "N/A"}, {"name": "storagevault login", "description": "Login to your Storagevault account", "permissions": "N/A", "aliases": [], "category": "storagevault", "arguments": "N/A"}, {"name": "storagevault logout", "description": "Logout from your Storagevault account", "permissions": "N/A", "aliases": [], "category": "storagevault", "arguments": "N/A"}, {"name": "storagevault upload", "description": "N/A", "permissions": "N/A", "aliases": [], "category": "storagevault", "arguments": "N/A"}, {"name": "storagevault users", "description": "N/A", "permissions": "N/A", "aliases": [], "category": "storagevault", "arguments": "N/A"}, {"name": "trn", "description": "Close the current ticket", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "delvouch", "description": "Delete a vouch", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "vouch_id"}, {"name": "instances", "description": "Manage the bot's instances", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "instances list", "description": "List all instances", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "instances setup", "description": "Setup a new instance", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "instance, owner, server"}, {"name": "instances remove", "description": "Remove an instance server", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "instance, server"}, {"name": "instances inspect", "description": "Inspect all instances from a user", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user"}, {"name": "instances add", "description": "Add a new instance server", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "instance, server"}, {"name": "instances transfer", "description": "Transfer an instance", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "instances transfer server", "description": "Transfer an instance server", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "instance, old_server, new_server"}, {"name": "instances transfer owner", "description": "Transfer an instance owner", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "old_owner, new_owner"}, {"name": "wipeall", "description": "Wipe all data from an user", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "user"}, {"name": "clownboard", "description": "Showcase the best messages in your server", "permissions": "N/A", "aliases": [], "category": "clownboard", "arguments": "N/A"}, {"name": "clownboard channel", "description": "Sets the channel where clownboard messages will be sent to", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "channel"}, {"name": "clownboard emoji", "description": "Sets the emoji that triggers the clownboard messages", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "emoji"}, {"name": "clownboard config", "description": "View the settings for clownboard in guild", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "N/A"}, {"name": "clownboard reset", "description": "Resets guild's configuration for clownboard", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "N/A"}, {"name": "clownboard ignore", "description": "Ignore channels from clownboard", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "N/A"}, {"name": "clownboard ignore add", "description": "Add a channel to the ignore list", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "target"}, {"name": "clownboard ignore remove", "description": "Remove a channel from the ignore list", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "target"}, {"name": "clownboard count", "description": "Sets the default amount stars needed to post", "permissions": "manage guild", "aliases": [], "category": "clownboard", "arguments": "count"}, {"name": "clownboard enable", "description": "Enable the suggestion module", "permissions": "manage guild", "aliases": ["on"], "category": "clownboard", "arguments": "N/A"}, {"name": "clownboard disable", "description": "Disable the suggestion module", "permissions": "manage guild", "aliases": ["off"], "category": "clownboard", "arguments": "N/A"}, {"name": "delavatar", "description": "Deletes a specific avatar from R2 bucket and the database", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "avatar"}, {"name": "tl", "description": "Lock a thread", "permissions": "manage threads", "aliases": [], "category": "moderation", "arguments": "thread"}, {"name": "github", "description": "Gets profile information on the given Github user", "permissions": "N/A", "aliases": ["git"], "category": "social", "arguments": "username"}, {"name": "investment", "description": "Manage your investments", "permissions": "N/A", "aliases": ["invest"], "category": "economy", "arguments": "N/A"}, {"name": "investment complete", "description": "Complete your investment", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "investment start", "description": "Start an investment", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "investment status", "description": "View the status of your investment", "permissions": "N/A", "aliases": ["info"], "category": "economy", "arguments": "N/A"}, {"name": "investment list", "description": "List all investments that exist", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "redeem", "description": "Redeem your product keys", "permissions": "N/A", "aliases": ["claim"], "category": "donor", "arguments": "key"}, {"name": "snapchat", "description": "Get bitmoji and QR scan code for user", "permissions": "N/A", "aliases": ["snap"], "category": "social", "arguments": "username"}, {"name": "roblo<PERSON>", "description": "Gets profile information on the given Roblox user", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "username"}, {"name": "selfpurge", "description": "Delete your own messages", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "amount"}, {"name": "cashapp", "description": "Retrieve simple CashApp profile information", "permissions": "N/A", "aliases": ["ca"], "category": "social", "arguments": "cashtag"}, {"name": "gunsinfo", "description": "Gets profile information on the given Guns.lol user", "permissions": "N/A", "aliases": ["guns"], "category": "social", "arguments": "username"}, {"name": "gunsinfouid", "description": "Gets profile information on the given Guns.lol UID", "permissions": "N/A", "aliases": ["gunsuid"], "category": "social", "arguments": "uid"}, {"name": "confessions", "description": "Setup the confessions module", "permissions": "N/A", "aliases": ["conf"], "category": "confessions", "arguments": "N/A"}, {"name": "confessions disable", "description": "Disable the confessions module", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "N/A"}, {"name": "confessions enable", "description": "Enable the confessions module", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "channel"}, {"name": "confessions unmute", "description": "Unmute a user from sending confessions", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "id"}, {"name": "confessions muted", "description": "List all confessions muted users", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "N/A"}, {"name": "confessions channel", "description": "Set the confession channel", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "channel"}, {"name": "confessions mute", "description": "Mute a user from sending confessions", "permissions": "manage_guild", "aliases": [], "category": "confessions", "arguments": "id"}, {"name": "gunslink", "description": "Connect your Discord account to Guns.lol", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "uid"}, {"name": "snapchatstory", "description": "Gets all current stories for the given Snapchat user", "permissions": "N/A", "aliases": ["snapstory"], "category": "social", "arguments": "username"}, {"name": "nowplaying", "description": "Get the latest song scrobbled on Last.Fm", "permissions": "N/A", "aliases": ["np", "fm"], "category": "lastfm", "arguments": "member"}, {"name": "language", "description": "View your current languages or somebody else's", "permissions": "N/A", "aliases": ["lang"], "category": "utility", "arguments": "user"}, {"name": "language list", "description": "View a list of every member's languages", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "language add", "description": "Add a language", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "language"}, {"name": "language translate", "description": "Translate a message to a specific language", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "language, ephemeral"}, {"name": "language remove", "description": "Remove a specific language", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "language"}, {"name": "safemode", "description": "Toggle the safemode on or off", "permissions": "N/A", "aliases": ["safesearch"], "category": "utility", "arguments": "mode"}, {"name": "instagramposts", "description": "Fetch Instagram post from a username", "permissions": "N/A", "aliases": ["igposts"], "category": "social", "arguments": "username"}, {"name": "application", "description": "Manage your applications", "permissions": "N/A", "aliases": ["app"], "category": "application", "arguments": "N/A"}, {"name": "application channel", "description": "Set the channel for an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, channel"}, {"name": "application delete", "description": "Delete an application", "permissions": "manage guild", "aliases": ["remove"], "category": "application", "arguments": "name"}, {"name": "application status", "description": "Change the status of an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, status"}, {"name": "application list", "description": "List all applications", "permissions": "manage guild", "aliases": ["all"], "category": "application", "arguments": "N/A"}, {"name": "application role", "description": "Manage application roles", "permissions": "manage guild", "aliases": ["roles"], "category": "application", "arguments": "N/A"}, {"name": "application role list", "description": "List all roles in an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name"}, {"name": "application role add", "description": "Add a role to an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, role"}, {"name": "application role remove", "description": "Remove a role from an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, role"}, {"name": "application create", "description": "Create a new application", "permissions": "manage guild", "aliases": ["add"], "category": "application", "arguments": "name"}, {"name": "application level", "description": "Set the level for an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, level"}, {"name": "application question", "description": "Manage questions", "permissions": "manage guild", "aliases": ["questions"], "category": "application", "arguments": "N/A"}, {"name": "application question remove", "description": "Remove a question from an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, question"}, {"name": "application question add", "description": "Add a question to an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, question"}, {"name": "application question edit", "description": "Edit a question in an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name, question, new_question"}, {"name": "application question list", "description": "List all questions in an application", "permissions": "manage guild", "aliases": [], "category": "application", "arguments": "name"}, {"name": "forcenick", "description": "Managing force nicknames", "permissions": "manage nicknames, donator", "aliases": ["fn"], "category": "donor", "arguments": "N/A"}, {"name": "forcenick set", "description": "Set or update a member's forced nickname", "permissions": "manage nicknames, donator", "aliases": [], "category": "donor", "arguments": "member, nickname"}, {"name": "forcenick remove", "description": "Remove a force nickname from a member", "permissions": "manage nicknames, donator", "aliases": [], "category": "donor", "arguments": "member"}, {"name": "forcenick list", "description": "List all members with force nicknames", "permissions": "manage nicknames, donator", "aliases": [], "category": "donor", "arguments": "N/A"}, {"name": "case", "description": "Manage cases", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "case info", "description": "Get information about a case", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "case"}, {"name": "case proof", "description": "Add proof to a case", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "case, proof"}, {"name": "case reason", "description": "Change the reason of a case", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "case, reason"}, {"name": "instagrampost", "description": "<PERSON>tch Instagram post from a URL", "permissions": "N/A", "aliases": ["igpost"], "category": "social", "arguments": "url"}, {"name": "instagramstory", "description": "Gets all current stories for the given Instagram user", "permissions": "N/A", "aliases": ["igstory"], "category": "social", "arguments": "username"}, {"name": "fyp", "description": "Repost a TikTok video from the FYP", "permissions": "N/A", "aliases": ["foryou", "foryoupage"], "category": "utility", "arguments": "N/A"}, {"name": "onlyfans", "description": "Gets all current posts for the given Onlyfans user", "permissions": "N/A", "aliases": ["of"], "category": "social", "arguments": "username"}, {"name": "hardban", "description": "Keep a member banned from the server", "permissions": "administrator & antinuke admin", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "unhardban", "description": "<PERSON><PERSON><PERSON> a hardbanned member", "permissions": "administrator & antinuke admin", "aliases": [], "category": "moderation", "arguments": "user, reason"}, {"name": "minecraft", "description": "Gets profile information on the given Minecraft player", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "username"}, {"name": "image", "description": "Search for an image on Google", "permissions": "N/A", "aliases": ["img"], "category": "utility", "arguments": "query"}, {"name": "revokefiles", "description": "Remove file attachment permissions from a member", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "state, member, reason"}, {"name": "instagram", "description": "Gets profile information on the given Instagram user", "permissions": "N/A", "aliases": ["ig"], "category": "instagram", "arguments": "username"}, {"name": "instagram list", "description": "View list of every Instagram feed", "permissions": "manage server", "aliases": [], "category": "instagram", "arguments": "N/A"}, {"name": "instagram remove", "description": "Removes an existing feed for a user", "permissions": "manage server", "aliases": [], "category": "instagram", "arguments": "username"}, {"name": "instagram check", "description": "Get the latest post from a user", "permissions": "N/A", "aliases": [], "category": "instagram", "arguments": "username"}, {"name": "instagram add", "description": "Create a new feed for a user", "permissions": "manage server", "aliases": [], "category": "instagram", "arguments": "username, channel, message"}, {"name": "fortnite", "description": "Fortnite related commands", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "N/A"}, {"name": "fortnite item", "description": "Searches for a Fortnite item by name and fetches its details", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "cosmetic"}, {"name": "fortnite shop", "description": "Show daily fortnite shop rotation", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "N/A"}, {"name": "fortnite lifetime", "description": "Gets lifetime stats on the given fortnite player", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "username"}, {"name": "fortnite season", "description": "Gets season stats on the given fortnite player", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "username"}, {"name": "google", "description": "Search Google for a query", "permissions": "N/A", "aliases": ["search"], "category": "utility", "arguments": "query"}, {"name": "authorized", "description": "Manage authorized users", "permissions": "bot manager", "aliases": ["auth"], "category": "manager", "arguments": "N/A"}, {"name": "authorized add", "description": "Add a user to the authorized list", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user, server"}, {"name": "authorized remove", "description": "Remove a user from the authorized list", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "user, server"}, {"name": "authorized list", "description": "List all authorized servers", "permissions": "bot manager", "aliases": [], "category": "manager", "arguments": "N/A"}, {"name": "antinuke", "description": "Antinuke & antiraid commands", "permissions": "N/A", "aliases": ["an"], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke botadd", "description": "Protect your server against new bot additions", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke botadd disable", "description": "Disable protection against new bot additions", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke botadd enable", "description": "Enable protection against new bot additions", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "punishment"}, {"name": "antinuke channelcreate", "description": "Prevent admins from creating channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke channelcreate disable", "description": "Disable protection against creating channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke channelcreate enable", "description": "Enable protection against creating channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke staff", "description": "Manage the roles that are marked as staff", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke staff remove", "description": "Remove a role as staff role", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "role"}, {"name": "antinuke staff list", "description": "View all staff roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke staff add", "description": "Add a role as staff role", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "role"}, {"name": "antinuke rolecreate", "description": "Prevent admins from creating roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolecreate disable", "description": "Disable protection against creating roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolecreate enable", "description": "Enable protection against creating roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke editrole", "description": "Prevent admins from editing dangerous roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke editrole disable", "description": "Disable protection against editing dangerous attributes of a role", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke editrole enable", "description": "Enable protection against editing dangerous attributes of a role", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "punishment"}, {"name": "antinuke whitelist", "description": "Whitelist a user or role from Antinuke system", "permissions": "antinuke admin", "aliases": ["wl"], "category": "antinuke", "arguments": "target"}, {"name": "antinuke kick", "description": "Prevent admins from kicking members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke kick enable", "description": "Enable protection against kicking members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke kick disable", "description": "Disable protection against kicking members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke setup", "description": "Enable Antinuke system to protect your server", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke reset", "description": "Disable Antinuke system on your server", "permissions": "antinuke owner", "aliases": ["disable"], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke giverole", "description": "Prevent admins from giving dangerous roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke giverole enable", "description": "Enable protection against giving dangerous roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "punishment"}, {"name": "antinuke giverole disable", "description": "Disable protection against giving dangerous roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke config", "description": "View Antinuke config on your server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke admin", "description": "Manage the members that can change the Antinuke settings", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke admin remove", "description": "Remove a user permissions to edit Antinuke settings", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "member"}, {"name": "antinuke admin list", "description": "View Antinuke admins on your server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke admin add", "description": "Give a user permissions to edit Antinuke settings", "permissions": "antinuke owner", "aliases": [], "category": "antinuke", "arguments": "member"}, {"name": "antinuke logs", "description": "Add/Remove Antinuke logs channel", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "channel"}, {"name": "antinuke vanity", "description": "Protect your server against vanity changes", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke vanity enable", "description": "Enable protaction against vanity change", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "punishment"}, {"name": "antinuke vanity disable", "description": "Disable protection against vanity change", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolelock", "description": "Manage role locks on your server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolelock list", "description": "List all locked roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolelock add", "description": "Add a role to the lock list", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "role"}, {"name": "antinuke rolelock enable", "description": "Enable all role locks on the server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolelock disable", "description": "Disable all role locks on the server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke rolelock remove", "description": "Remove a role from the lock list", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "role"}, {"name": "antinuke unwhitelist", "description": "Unwhitelist a user or role from Antinuke system", "permissions": "antinuke admin", "aliases": ["uwl"], "category": "antinuke", "arguments": "target"}, {"name": "antinuke restore", "description": "Restore roles after an antinuke punishment", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "member"}, {"name": "antinuke channeldelete", "description": "Prevent admins from deleting channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke channeldelete enable", "description": "Enable protection against deleting channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke channeldelete disable", "description": "Disable protection against deleting channels", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke roledelete", "description": "Prevent admins from deleting roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke roledelete enable", "description": "Enable protection against deleting roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke roledelete disable", "description": "Disable protection against deleting roles", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke whitelisted", "description": "View AntiNuke whitelisted members & bots on your server", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke ban", "description": "Prevent admins from banning members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "antinuke ban enable", "description": "Enable protection against banning members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "threshold, punishment"}, {"name": "antinuke ban disable", "description": "Disable protection against banning members", "permissions": "antinuke admin", "aliases": [], "category": "antinuke", "arguments": "N/A"}, {"name": "valorant", "description": "Gets profile information on the given Valorant user", "permissions": "N/A", "aliases": ["val"], "category": "social", "arguments": "username"}, {"name": "reload", "description": "Reload modules", "permissions": "bot developer", "aliases": ["rl"], "category": "developer", "arguments": "module"}, {"name": "selfprefix", "description": "Manage your self prefix", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "N/A"}, {"name": "selfprefix leaderboard", "description": "View the leaderboard of selfprefixes used for the bot", "permissions": "N/A", "aliases": ["lb"], "category": "donor", "arguments": "N/A"}, {"name": "selfprefix set", "description": "Set your self prefix", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "prefix"}, {"name": "selfprefix remove", "description": "Remove your self prefix", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "N/A"}, {"name": "vanity", "description": "Manage vanity settings", "permissions": "N/A", "aliases": ["van"], "category": "vanity", "arguments": "N/A"}, {"name": "vanity message", "description": "Set the vanity message", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "message"}, {"name": "vanity remove", "description": "Remove the vanity substring", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "vanity set", "description": "Set the vanity substring", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "vanity"}, {"name": "vanity channel", "description": "Manage vanity channel", "permissions": "N/A", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "vanity channel set", "description": "Set the vanity channel", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "channel"}, {"name": "vanity channel remove", "description": "Remove the vanity channel", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "vanity role", "description": "Manage vanity reward roles", "permissions": "N/A", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "vanity role add", "description": "Add a role as vanity reward role", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "role"}, {"name": "vanity role remove", "description": "Remove a role from vanity reward roles", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "role"}, {"name": "vanity role list", "description": "List all vanity reward roles", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "vanity config", "description": "Show the vanity settings for the guild.", "permissions": "manage guild", "aliases": [], "category": "vanity", "arguments": "N/A"}, {"name": "steam", "description": "Gets profile information on the given Steam user", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "steamid"}, {"name": "stickymessage", "description": "Set up a sticky message in one or multiple channels", "permissions": "N/A", "aliases": ["stickymsg", "sticky"], "category": "utility", "arguments": "N/A"}, {"name": "stickymessage remove", "description": "Remove a sticky message from a channel", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "channel"}, {"name": "stickymessage list", "description": "List all channels with a sticky message", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "stickymessage add", "description": "Add a sticky message to a channel", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "channel, code"}, {"name": "reminder", "description": "Get reminders for a duration set about whatever you choose", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "reminder add", "description": "Add a reminder", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "time, task"}, {"name": "reminder remove", "description": "Remove a reminder by its position in the list", "permissions": "N/A", "aliases": ["cancel"], "category": "utility", "arguments": "id"}, {"name": "reminder list", "description": "View a list of every reminder", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "lastfm", "description": "Use the Last.fm API integration with evelina", "permissions": "N/A", "aliases": ["lf"], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm spotify", "description": "Look up for your nowplaying lastfm song on spotify", "permissions": "N/A", "aliases": ["sp"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm taste", "description": "Compare your music taste between you and someone else", "permissions": "N/A", "aliases": [], "category": "lastfm", "arguments": "member"}, {"name": "lastfm topartists", "description": "Get the top artists of a user", "permissions": "N/A", "aliases": ["ta", "tar"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm cover", "description": "Get the cover image of your lastfm song", "permissions": "N/A", "aliases": ["image"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm toptracks", "description": "Get the top tracks of a user", "permissions": "N/A", "aliases": ["tt"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm recent", "description": "Get the most recent songs of a user", "permissions": "N/A", "aliases": [], "category": "lastfm", "arguments": "member"}, {"name": "lastfm topalbums", "description": "Get the top albums of a user", "permissions": "N/A", "aliases": ["tal"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm howto", "description": "A short guide on how to register your lastfm account", "permissions": "N/A", "aliases": [], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm set", "description": "Login and authenticate <PERSON><PERSON> to use your account", "permissions": "N/A", "aliases": ["login", "connect"], "category": "lastfm", "arguments": "username"}, {"name": "lastfm crowns", "description": "Get users with the most crowns", "permissions": "N/A", "aliases": ["crown"], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm user", "description": "Get information about a user's lastfm account", "permissions": "N/A", "aliases": ["ui", "profile"], "category": "lastfm", "arguments": "member"}, {"name": "lastfm unset", "description": "Logout and disconnect evelina from your account", "permissions": "N/A", "aliases": ["logout", "disconnect"], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm playing", "description": "See what song everyone is listening to in a server", "permissions": "N/A", "aliases": [], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm globalwhoknows", "description": "Get the top listeners of a certain artist from the server", "permissions": "N/A", "aliases": ["gwk"], "category": "lastfm", "arguments": "artist"}, {"name": "lastfm whoknows", "description": "Get the top listeners of a certain artist from the server", "permissions": "N/A", "aliases": ["wk"], "category": "lastfm", "arguments": "artist"}, {"name": "lastfm chart", "description": "View a collage of your most listened to albums", "permissions": "N/A", "aliases": ["c"], "category": "lastfm", "arguments": "member, size, period"}, {"name": "lastfm plays", "description": "Get the total amount of plays of a user for certain artist", "permissions": "N/A", "aliases": ["p"], "category": "lastfm", "arguments": "user, artist"}, {"name": "lastfm whoknowsalbum", "description": "Get the top listeners of a certain album from the server", "permissions": "N/A", "aliases": ["wka"], "category": "lastfm", "arguments": "album"}, {"name": "lastfm globalwkalbum", "description": "Get the top listeners of a certain album from the server", "permissions": "N/A", "aliases": ["gwka"], "category": "lastfm", "arguments": "album"}, {"name": "lastfm customcommand", "description": "Set a custom command for nowplaying", "permissions": "N/A", "aliases": ["cc"], "category": "lastfm", "arguments": "cmd"}, {"name": "lastfm whoknowstrack", "description": "Get the top listeners of a certain track from the server", "permissions": "N/A", "aliases": ["wkt"], "category": "lastfm", "arguments": "track"}, {"name": "lastfm mode", "description": "Use a different embed for now playing or create your own", "permissions": "N/A", "aliases": ["embed"], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm mode view", "description": "View your custom lastfm embed or someone's lastfm embed", "permissions": "donator", "aliases": [], "category": "lastfm", "arguments": "member"}, {"name": "lastfm mode set", "description": "Set a custom embed for nowplaying", "permissions": "donator", "aliases": [], "category": "lastfm", "arguments": "code"}, {"name": "lastfm mode steal", "description": "Steal someone's lastfm embed", "permissions": "donator", "aliases": [], "category": "lastfm", "arguments": "member"}, {"name": "lastfm mode reply", "description": "Enable or disable if bot should reply with lastfm message", "permissions": "donator", "aliases": [], "category": "lastfm", "arguments": "option"}, {"name": "lastfm mode remove", "description": "Remove your custom lastfm embed", "permissions": "donator", "aliases": [], "category": "lastfm", "arguments": "N/A"}, {"name": "lastfm reactions", "description": "Set custom reactions for the nowplaying command", "permissions": "N/A", "aliases": [], "category": "lastfm", "arguments": "reactions"}, {"name": "lastfm globalwktrack", "description": "Get the top listeners of a certain track from the server", "permissions": "N/A", "aliases": ["gwkt"], "category": "lastfm", "arguments": "track"}, {"name": "appeal", "description": "Manage appeals", "permissions": "manage server", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "appeal list", "description": "List the appeals channel", "permissions": "manage server", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "appeal remove", "description": "Remove the appeals channel", "permissions": "manage server", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "appeal set", "description": "Set the appeals channel", "permissions": "manage server", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "clashofclans", "description": "Clash of Clans related commands", "permissions": "N/A", "aliases": ["coc"], "category": "social", "arguments": "N/A"}, {"name": "clashofclans verify", "description": "Verify your Clash of Clans account", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "tag"}, {"name": "clashofclans info", "description": "Gets profile information on the given Clash of Clans player", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "tag"}, {"name": "clashofclans heroes", "description": "Gets hero information on the given Clash of Clans player", "permissions": "N/A", "aliases": [], "category": "social", "arguments": "tag"}, {"name": "logs", "description": "Log events in your server", "permissions": "N/A", "aliases": ["logging"], "category": "logging", "arguments": "N/A"}, {"name": "logs list", "description": "List all the logging channels", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs setup", "description": "Set up all logging channels in a category", "permissions": "Setup logging channels", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove", "description": "Remove logging for specific events", "permissions": "N/A", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove moderation", "description": "Stop logging moderation related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove channels", "description": "Stop logging channel related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove voice", "description": "Stop logging voice related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove guild", "description": "Stop logging guild related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove members", "description": "Stop logging member related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove messages", "description": "Stop logging message related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs remove roles", "description": "Stop logging role related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs add", "description": "Add logging for specific events", "permissions": "N/A", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs add guild", "description": "Log guild related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add channels", "description": "Log channel related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add members", "description": "Log member related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add voice", "description": "Log voice related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add roles", "description": "Log role related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add moderation", "description": "Log moderation related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs add messages", "description": "Log message related events", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "channel"}, {"name": "logs ignore", "description": "Manage the logging ignore list", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs ignore add", "description": "Add a user, role or channel to the logging ignore list", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "target"}, {"name": "logs ignore list", "description": "View the logging ignore list", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "N/A"}, {"name": "logs ignore remove", "description": "Remove a user, role or channel from the logging ignore list", "permissions": "manage guild", "aliases": [], "category": "logging", "arguments": "target"}, {"name": "firstmessage", "description": "Get a link for the first message in a channel or by a user in a channel", "permissions": "N/A", "aliases": ["firstmsg"], "category": "utility", "arguments": "channel, user"}, {"name": "autopfp", "description": "Automatically send pfps to a channel", "permissions": "N/A", "aliases": [], "category": "autopost", "arguments": "N/A"}, {"name": "autopfp remove", "description": "Remove an autopfp channel", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "category"}, {"name": "autopfp avatar", "description": "Change the way how the bot webhook is avatar", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "category, avatar"}, {"name": "autopfp name", "description": "Change the way how the bot webhook is named", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "category, name"}, {"name": "autopfp add", "description": "Add an autopfp channel", "permissions": "manage server", "aliases": [], "category": "autopost", "arguments": "channel, category"}, {"name": "tag", "description": "View a tag", "permissions": "N/A", "aliases": ["tags"], "category": "utility", "arguments": "tag"}, {"name": "tag random", "description": "Return a random tag", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "tag reset", "description": "Reset every tag for this guild", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "tag search", "description": "Search for tags containing a keyword", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "query"}, {"name": "tag remove", "description": "Remove a tag from guild", "permissions": "manage guild", "aliases": ["delete", "del"], "category": "utility", "arguments": "tag"}, {"name": "tag list", "description": "View a list of every tag in guild", "permissions": "manage guild", "aliases": [], "category": "utility", "arguments": "N/A"}, {"name": "tag edit", "description": "Edit the contents of your tag", "permissions": "tag owner", "aliases": [], "category": "utility", "arguments": "args"}, {"name": "tag author", "description": "View the author of a tag", "permissions": "N/A", "aliases": ["creator"], "category": "utility", "arguments": "tag"}, {"name": "tag add", "description": "Add a tag to guild", "permissions": "manage guild", "aliases": ["create"], "category": "utility", "arguments": "args"}, {"name": "avatar", "description": "Get avatar of a member or yourself", "permissions": "N/A", "aliases": ["av"], "category": "utility", "arguments": "member"}, {"name": "<PERSON><PERSON>s", "description": "Create your own shortcuts for commands", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "N/A"}, {"name": "selfalias remove", "description": "Remove an self<PERSON>s for command", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "alias"}, {"name": "selfalias list", "description": "List every selfalias for all commands", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "N/A"}, {"name": "selfalias add", "description": "Create or update a selfalias for command", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "alias, command, args"}, {"name": "interface", "description": "Create a custom voice master interface", "permissions": "administrator", "aliases": [], "category": "voicemaster", "arguments": "channel, code"}, {"name": "getinvite", "description": "Get the invite of a authorized server", "permissions": "bot developer", "aliases": [], "category": "developer", "arguments": "guild"}, {"name": "serveravatar", "description": "Get the server avatar of a member or yourself", "permissions": "N/A", "aliases": ["sav", "savatar"], "category": "utility", "arguments": "member"}, {"name": "banner", "description": "Get the banner of a member or yourself", "permissions": "N/A", "aliases": [], "category": "utility", "arguments": "member"}, {"name": "anowner", "description": "Change the antinuke owner of a guild", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "guild, member"}, {"name": "premium", "description": "Premium commands", "permissions": "bot supporter", "aliases": ["prem"], "category": "supporter", "arguments": "N/A"}, {"name": "premium check", "description": "Check if a server has premium", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "invite"}, {"name": "premium inspect", "description": "Inspect all premium guilds of a member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "user"}, {"name": "premium transfer", "description": "Transfer premium from one guild to another", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "old_inv, new_inv"}, {"name": "premium add", "description": "Add premium to a guild", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "member, invite"}, {"name": "premium remove", "description": "Remove premium from a guild", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "invite"}, {"name": "premium list", "description": "List all premium guilds", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "N/A"}, {"name": "choose", "description": "Choose between options", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "choices"}, {"name": "tm", "description": "Move the ticket to another category", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "cleanup", "description": "Cleanup your server database", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "cleanup role", "description": "Delete a channel from database", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "role"}, {"name": "cleanup channel", "description": "Delete a role from datebase", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "quickpoll", "description": "Create a quick poll", "permissions": "N/A", "aliases": ["poll", "qp"], "category": "fun", "arguments": "question"}, {"name": "roll", "description": "Get a random number between the given range", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "min, max"}, {"name": "business", "description": "Manage your business", "permissions": "N/A", "aliases": ["b"], "category": "economy", "arguments": "N/A"}, {"name": "business sell", "description": "Sell your business", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "business buy", "description": "Buy a business", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "business"}, {"name": "business list", "description": "List all businesses", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "business add", "description": "Add a business to a user", "permissions": "bot helper", "aliases": [], "category": "economy", "arguments": "user, business_id"}, {"name": "business collect", "description": "Collect the balance from your business", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "business remove", "description": "Remove a business from a user", "permissions": "bot helper", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "business info", "description": "Get information about your business", "permissions": "N/A", "aliases": ["status"], "category": "economy", "arguments": "user"}, {"name": "song", "description": "Get information about a song", "permissions": "N/A", "aliases": ["music", "beat", "songinfo"], "category": "fun", "arguments": "title"}, {"name": "inventory", "description": "View your inventory", "permissions": "N/A", "aliases": ["inv"], "category": "economy", "arguments": "user"}, {"name": "dog", "description": "Get a random dog image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "freegames", "description": "N/A", "permissions": "N/A", "aliases": ["fg"], "category": "config", "arguments": "N/A"}, {"name": "freegames list", "description": "List all freegames channels", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "freegames add", "description": "Add a channel to freegames", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "freegames remove", "description": "Remove a channel from freegames", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "cat", "description": "Get a random cat image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "reverse", "description": "Search Google for similar images using reverse image search", "permissions": "donator", "aliases": ["reverseimage", "reverseimg"], "category": "donor", "arguments": "url"}, {"name": "bird", "description": "Get a random bird image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "impersonate", "description": "Impersonate a member", "permissions": "donator", "aliases": ["imp"], "category": "donor", "arguments": "member, message"}, {"name": "capybara", "description": "Get a random capybara image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Uwuify messages of a specific user", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "member"}, {"name": "monkey", "description": "Get a random monkey image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "deeplookup", "description": "Deep lookup a member", "permissions": "donator", "aliases": ["dlu", "deep"], "category": "donor", "arguments": "user"}, {"name": "ask", "description": "Ask the AI something", "permissions": "donator", "aliases": ["ai"], "category": "donor", "arguments": "prompt"}, {"name": "meme", "description": "Get a random meme", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Get a random dad joke", "permissions": "N/A", "aliases": ["cringejoke"], "category": "fun", "arguments": "N/A"}, {"name": "alias", "description": "Create your own shortcuts for commands", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "alias remove", "description": "Remove an alias for command", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "alias"}, {"name": "alias list", "description": "List every alias for all commands", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "alias add", "description": "Create or update an alias for command", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "alias, command, args"}, {"name": "pingtimeout", "description": "Manage ping timeout", "permissions": "N/A", "aliases": ["pto"], "category": "config", "arguments": "N/A"}, {"name": "pingtimeout add", "description": "Add a role to ping timeout", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "role, timeout"}, {"name": "pingtimeout remove", "description": "Remove a role from ping timeout", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "role"}, {"name": "pingtimeout list", "description": "List all ping timeout roles", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "tiktok", "description": "Gets profile information on the given TikTok user", "permissions": "N/A", "aliases": ["tt"], "category": "tiktok", "arguments": "username"}, {"name": "tiktok check", "description": "Get the latest video from a user", "permissions": "N/A", "aliases": [], "category": "tiktok", "arguments": "username"}, {"name": "tik<PERSON> remove", "description": "Removes an existing feed for a user", "permissions": "manage server", "aliases": [], "category": "tiktok", "arguments": "username"}, {"name": "tiktok list", "description": "List all TikTok user feeds", "permissions": "manage server", "aliases": [], "category": "tiktok", "arguments": "N/A"}, {"name": "tiktok add", "description": "Create a new feed for a user", "permissions": "manage server", "aliases": [], "category": "tiktok", "arguments": "username, channel, message"}, {"name": "transcribe", "description": "Transcribe an audio file", "permissions": "donator", "aliases": [], "category": "donor", "arguments": "url"}, {"name": "advice", "description": "Get a random advice", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "uselessfact", "description": "Get a random useless fact", "permissions": "N/A", "aliases": ["fact", "uf"], "category": "fun", "arguments": "N/A"}, {"name": "error", "description": "View information about an error code", "permissions": "bot supporter", "aliases": ["trace"], "category": "supporter", "arguments": "code"}, {"name": "rizz", "description": "Get a random rizz", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "modstats", "description": "View a summary of a moderator's punishment statistics", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "user"}, {"name": "voicemove", "description": "Move a member to another voice channel", "permissions": "move members", "aliases": ["drag"], "category": "moderation", "arguments": "member"}, {"name": "gay", "description": "Gay rate yourself or a given member", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "member"}, {"name": "lab", "description": "Buy a laboratory business which generates money over time and needs to be maintained", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "lab add", "description": "Add a laboratory to a user", "permissions": "bot moderator", "aliases": [], "category": "economy", "arguments": "user, upgrade_state"}, {"name": "lab sell", "description": "Sell your laboratory business", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "lab upgrade", "description": "Upgrade your laboratory business by one level.", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "lab ampoules", "description": "Buy ampoules for your laboratory", "permissions": "N/A", "aliases": ["restock", "ba"], "category": "economy", "arguments": "amount"}, {"name": "lab info", "description": "Check the status of your laboratory business", "permissions": "N/A", "aliases": ["status"], "category": "economy", "arguments": "user"}, {"name": "lab remove", "description": "Remove a laboratory from a user", "permissions": "bot moderator", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "lab buy", "description": "Buy a laboratory business", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "lab collect", "description": "Collect your laboratory business earnings", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "furry", "description": "Furry rate yourself or a given member", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "member"}, {"name": "mutuals", "description": "Returns mutual servers between the member and the bot with their permissions", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "user"}, {"name": "clear", "description": "Add/remove roles to/from a user", "permissions": "manage messages", "aliases": ["purge", "c"], "category": "moderation", "arguments": "number, user"}, {"name": "clear links", "description": "Clear messages that contain links", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "clear images", "description": "Clear messages that have attachments", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "clear messages", "description": "Delete multiple messages at once (up to 100)", "permissions": "Manage messages", "aliases": [], "category": "moderation", "arguments": "number, user"}, {"name": "clear bots", "description": "Clear messages sent by bots", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "clear invites", "description": "Clear messages that contain discord invite links", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "clear contains", "description": "Clear messages that contain a certain word", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "word"}, {"name": "clear embeds", "description": "Clear messages that have embeds", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "clear mentions", "description": "Clear message that have certain mentions", "permissions": "manage message", "aliases": [], "category": "moderation", "arguments": "user"}, {"name": "pp", "description": "Check pp size from yourself or a given member", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "member"}, {"name": "voicemoveall", "description": "Move all members from a specific voice channel to the author's channel", "permissions": "Move all members", "aliases": ["dragall"], "category": "moderation", "arguments": "channel, other_channel"}, {"name": "voicekick", "description": "Kick a member from a voice channel", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "vape", "description": "Hit the vape", "permissions": "N/A", "aliases": ["juul"], "category": "fun", "arguments": "N/A"}, {"name": "vape leaderboard", "description": "Global leaderboard for vape hits across all servers", "permissions": "N/A", "aliases": ["lb"], "category": "fun", "arguments": "N/A"}, {"name": "vape hits", "description": "View the amount of hits in the server", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "vape steal", "description": "Steal the vape from a member", "permissions": "N/A", "aliases": ["take"], "category": "fun", "arguments": "N/A"}, {"name": "vape hit", "description": "Hit the vape", "permissions": "N/A", "aliases": ["smoke"], "category": "fun", "arguments": "N/A"}, {"name": "revive", "description": "Revive a channel", "permissions": "N/A", "aliases": ["rv"], "category": "config", "arguments": "N/A"}, {"name": "revive list", "description": "List all revive channels", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "revive add", "description": "Add a channel to revive", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "channel, timeout, code"}, {"name": "revive remove", "description": "Remove a channel from revive", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "voicemute", "description": "Voice mute a member", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "globalenable", "description": "Globally enable a command", "permissions": "bot moderator", "aliases": ["ge"], "category": "moderator", "arguments": "command"}, {"name": "voiceunmute", "description": "Voice unmute a member", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "usage", "description": "Usage commands", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "usage server", "description": "View all uses of a specific command by a server", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "server, command"}, {"name": "usage commands", "description": "View the top 100 most used commands", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "usage servers", "description": "View the top 100 most active servers", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "usage economy", "description": "Economy commands", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "usage economy user", "description": "View all economy transactions of a user", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "user"}, {"name": "usage user", "description": "View all uses of a specific command by a user", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "user, command"}, {"name": "usage users", "description": "View the top 100 most active users", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "usage command", "description": "View all uses of a specific command", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "command"}, {"name": "usage recent", "description": "View the most recent uses of a specific command", "permissions": "bot moderator", "aliases": [], "category": "moderator", "arguments": "command"}, {"name": "voicedeafen", "description": "De<PERSON>en a member in a voice channel", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "globaldisable", "description": "Globally disable a command", "permissions": "bot moderator", "aliases": ["gd"], "category": "moderator", "arguments": "command, reason"}, {"name": "voiceundeafen", "description": "Voice undeafen a member", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "restrictcommand", "description": "Only allows people with a certain role to use command", "permissions": "manage guild", "aliases": ["restrictcmd", "rc"], "category": "config", "arguments": "N/A"}, {"name": "restrictcommand remove", "description": "Removes the specified role's exclusive permission to use a command or cog", "permissions": "manage guild", "aliases": ["delete", "del"], "category": "config", "arguments": "role, command"}, {"name": "restrictcommand list", "description": "View a list of every restricted command", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "restrictcommand add", "description": "Allows the specified role exclusive permission to use a command or cog", "permissions": "manage guild", "aliases": ["make"], "category": "config", "arguments": "role, command"}, {"name": "globaldisabled", "description": "Show all commands that are globally disabled", "permissions": "bot moderator", "aliases": ["gdl"], "category": "moderator", "arguments": "N/A"}, {"name": "voiceban", "description": "Voice ban a member", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Voice unban a member", "permissions": "move members", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "help", "description": "Shows this message", "permissions": "N/A", "aliases": [], "category": "", "arguments": "command"}, {"name": "customquote", "description": "Create a custom quote", "permissions": "N/A", "aliases": ["cq"], "category": "fun", "arguments": "user, text"}, {"name": "smoke", "description": "Hit the vape", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "embed", "description": "Create embeds using the bot", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "embed load", "description": "Load a saved embed code", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "code"}, {"name": "embed copy", "description": "Copy the embed code of each embed in the message as separate messages", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "message"}, {"name": "embed post", "description": "Post an embed on <PERSON><PERSON>'s web", "permissions": "Embed Creator", "aliases": [], "category": "config", "arguments": "name, code"}, {"name": "embed create", "description": "Create an embed using an embed code\n> **Embed Builder:** https://evelina.bot/embed", "permissions": "manage messages", "aliases": [], "category": "config", "arguments": "code"}, {"name": "embed edit", "description": "Edit an embed sent by <PERSON><PERSON>", "permissions": "manage messages", "aliases": [], "category": "config", "arguments": "message, field, value"}, {"name": "embed save", "description": "Save the embed code of a certain embed", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "message"}, {"name": "embed overwrite", "description": "Overwrite an embed sent by <PERSON><PERSON>", "permissions": "manage messages", "aliases": [], "category": "config", "arguments": "message, code"}, {"name": "quote", "description": "Create a quote out of a user's message", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "message, bw"}, {"name": "instance", "description": "Instance commands", "permissions": "Instance Owner", "aliases": [], "category": "instance", "arguments": "N/A"}, {"name": "instance avatar", "description": "Change avatar of your bot instance", "permissions": "Instance Owner", "aliases": [], "category": "instance", "arguments": "url"}, {"name": "instance whitelist", "description": "Whitelist a server using a product key", "permissions": "Instance Owner", "aliases": [], "category": "instance", "arguments": "server"}, {"name": "instance banner", "description": "Change banner of your bot instance", "permissions": "Instance Owner", "aliases": [], "category": "instance", "arguments": "url"}, {"name": "instance status", "description": "Change status of your bot instance", "permissions": "Instance Owner", "aliases": [], "category": "instance", "arguments": "status"}, {"name": "instance description", "description": "Change description of your bot instance", "permissions": "Instance Owner", "aliases": ["bio"], "category": "instance", "arguments": "description"}, {"name": "jishaku", "description": "The Jishaku debug and diagnostic commands.\n\nThis command on its own gives a status brief.\nAll other functionality is within its subcommands.", "permissions": "N/A", "aliases": ["jsk"], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku node", "description": "Shortcut for scaffolding and executing 'npm run'. Only exists if the executables are detected.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku shutdown", "description": "Logs this bot out.", "permissions": "N/A", "aliases": ["logout"], "category": "jishaku", "arguments": "N/A"}, {"name": "ji<PERSON><PERSON> override", "description": "Run a command with a different user, channel, or thread, optionally bypassing checks and cooldowns.\n\nUsers will try to resolve to a Member, but will use a User if it can't find one.", "permissions": "N/A", "aliases": ["execute", "exec", "override!", "execute!", "exec!"], "category": "jishaku", "arguments": "overrides, command_string"}, {"name": "jishaku sync", "description": "Sync global or guild application commands to Discord.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "targets"}, {"name": "jishaku debug", "description": "Run a command timing execution and catching exceptions.", "permissions": "N/A", "aliases": ["dbg"], "category": "jishaku", "arguments": "command_string"}, {"name": "jishaku rtt", "description": "Calculates Round-Trip Time to the API.", "permissions": "N/A", "aliases": ["ping"], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku shell", "description": "Executes statements in the system shell.\n\nThis uses the system shell as defined in $SHELL, or `/bin/bash` otherwise.\nExecution can be cancelled by closing the paginator.", "permissions": "N/A", "aliases": ["bash", "sh", "powershell", "ps1", "ps", "cmd", "terminal"], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku source", "description": "Displays the source code for a command.", "permissions": "N/A", "aliases": ["src"], "category": "jishaku", "arguments": "command_name"}, {"name": "jishaku retain", "description": "Turn variable retention for REPL on or off.\n\nProvide no argument for current status.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "toggle"}, {"name": "jishaku cat", "description": "Read out a file, using syntax highlighting if detected.\n\nLines and linespans are supported by adding '#L12' or '#L12-14' etc to the end of the filename.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku py", "description": "Direct evaluation of Python code.", "permissions": "N/A", "aliases": ["python"], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku curl", "description": "Download and display a text file from the internet.\n\nThis command is similar to jsk cat, but accepts a URL.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "url"}, {"name": "jishaku sql", "description": "Parent for SQL adapter related commands", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku sql select", "description": "Shortcut for 'jsk sql fetch select'.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "query"}, {"name": "ji<PERSON><PERSON> sql fetchrow", "description": "Fetch a single row from the SQL database.", "permissions": "N/A", "aliases": ["fetchone"], "category": "jishaku", "arguments": "query"}, {"name": "<PERSON><PERSON><PERSON> sql execute", "description": "Executes a statement against the SQL database.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "query"}, {"name": "jishaku sql schema", "description": "Queries for the current schema and shows located table structures.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "query"}, {"name": "ji<PERSON><PERSON> sql fetch", "description": "Fetch multiple rows from the SQL database.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "query"}, {"name": "jishaku py_inspect", "description": "Evaluation of Python code with inspect information.", "permissions": "N/A", "aliases": ["pyi", "python_inspect", "pythoninspect"], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku permtrace", "description": "Calculates the source of granted or rejected permissions.\n\nThis accepts a channel, and either a member or a list of roles.\nIt calculates permissions the same way <PERSON>rd does, while keeping track of the source.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "channel, targets"}, {"name": "jishaku dis", "description": "Disassemble Python code into bytecode.", "permissions": "N/A", "aliases": ["disassemble"], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku show", "description": "Shows <PERSON><PERSON><PERSON> in the help command.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku ast", "description": "Disassemble Python code into AST.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku hide", "description": "<PERSON><PERSON> from the help command.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku specialist", "description": "Direct evaluation of Python code.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku tasks", "description": "Shows the currently running jishaku tasks.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku repeat", "description": "Runs a command multiple times in a row.\n\nThis acts like the command was invoked several times manually, so it obeys cooldowns.\nYou can use this in conjunction with `jsk sudo` to bypass this.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "times, command_string"}, {"name": "ji<PERSON><PERSON> voice", "description": "Voice-related commands.\n\nIf invoked without subcommand, relays current voice state.", "permissions": "N/A", "aliases": ["vc"], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku voice volume", "description": "Adjusts the volume of an audio source if it is supported.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "percentage"}, {"name": "ji<PERSON><PERSON> voice disconnect", "description": "Disconnects from the voice channel in this guild, if there is one.", "permissions": "N/A", "aliases": ["dc"], "category": "jishaku", "arguments": "N/A"}, {"name": "jishaku voice stop", "description": "Stops running an audio source, if there is one.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "ji<PERSON><PERSON> voice pause", "description": "Pauses a running audio source, if there is one.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "<PERSON><PERSON><PERSON> voice play", "description": "Plays audio direct from a URI.\n\nCan be either a local file or an audio resource on the internet.", "permissions": "N/A", "aliases": ["play_local"], "category": "jishaku", "arguments": "uri"}, {"name": "ji<PERSON><PERSON> voice resume", "description": "Resumes a running audio source, if there is one.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "N/A"}, {"name": "<PERSON><PERSON><PERSON> voice join", "description": "Joins a voice channel, or moves to it if already connected.\n\nPassing a voice channel uses that voice channel.\nPassing a member will use that member's current voice channel.\nPassing nothing will use the author's voice channel.", "permissions": "N/A", "aliases": ["connect"], "category": "jishaku", "arguments": "destination"}, {"name": "jishaku voice youtube_dl", "description": "Plays audio from youtube_dl-compatible sources.", "permissions": "N/A", "aliases": ["youtubedl", "ytdl", "yt"], "category": "jishaku", "arguments": "url"}, {"name": "jishaku cancel", "description": "Cancels a task with the given index.\n\nIf the index passed is -1, will cancel the last task instead.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "index"}, {"name": "jishaku invite", "description": "Retrieve the invite URL for this bot.\n\nIf the names of permissions are provided, they are requested as part of the invite.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "perms"}, {"name": "jishaku git", "description": "Shortcut for 'jsk sh git'. Invokes the system shell.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku load", "description": "Loads or reloads the given extension names.\n\nReports any extensions that failed to load.", "permissions": "N/A", "aliases": ["reload"], "category": "jishaku", "arguments": "extensions"}, {"name": "jishaku pip", "description": "Shortcut for 'jsk sh pip'. Invokes the system shell.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "argument"}, {"name": "jishaku unload", "description": "Unloads the given extension names.\n\nReports any extensions that failed to unload.", "permissions": "N/A", "aliases": [], "category": "jishaku", "arguments": "extensions"}, {"name": "restrictmodule", "description": "Restrict a module to a role", "permissions": "manage guild", "aliases": ["restrictmod", "rm"], "category": "config", "arguments": "N/A"}, {"name": "restrictmodule add", "description": "Restrict a module to a role", "permissions": "manage guild", "aliases": ["make"], "category": "config", "arguments": "role, module"}, {"name": "restrictmodule remove", "description": "Remove a module's restriction from a role", "permissions": "manage guild", "aliases": ["delete", "del"], "category": "config", "arguments": "role, module"}, {"name": "restrictmodule list", "description": "View a list of every restricted module", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "quotechannel", "description": "Create or remove a quote channel", "permissions": "N/A", "aliases": ["qc"], "category": "fun", "arguments": "channel"}, {"name": "ticket", "description": "Ticket commands", "permissions": "N/A", "aliases": ["t", "tickets"], "category": "ticket", "arguments": "N/A"}, {"name": "ticket unallow", "description": "Remove a user or role from the ticket", "permissions": "ticket support / manage channels", "aliases": ["remove"], "category": "ticket", "arguments": "target"}, {"name": "ticket close", "description": "Close the current ticket", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket closerequest", "description": "Request to close the current ticket", "permissions": "ticket support / manage channels", "aliases": ["cr"], "category": "ticket", "arguments": "N/A"}, {"name": "ticket claiming", "description": "Configure the ticket claiming system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket claiming disable", "description": "Disable the ticket claiming system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket claiming privat", "description": "Set the ticket claiming system to private", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket claiming public", "description": "Set the ticket claiming system to public", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket claiming enable", "description": "Enable the ticket claiming system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket send", "description": "Send the ticket panel to a channel", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "channel, code"}, {"name": "ticket modal", "description": "Modify modal for ticket topics", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket modal add", "description": "Add modal to a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, name, description"}, {"name": "ticket modal style", "description": "Change the style of a modal for a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, name, style"}, {"name": "ticket modal list", "description": "List all modals for all ticket topics", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket modal name", "description": "Edit modal name for a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, old, new"}, {"name": "ticket modal description", "description": "Edit modal description for a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, name, description"}, {"name": "ticket modal toggle", "description": "Toggle a modal for a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, name"}, {"name": "ticket modal remove", "description": "Remove modal from a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "topic, name"}, {"name": "ticket config", "description": "Check the server's ticket settings", "permissions": "N/A", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket move", "description": "Move the ticket to another category", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket blacklist", "description": "Blacklist a member from creating tickets", "permissions": "Manage guild", "aliases": [], "category": "ticket", "arguments": "user, reason"}, {"name": "ticket rename", "description": "Rename a ticket channel", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket limit", "description": "Set the maximum amount of tickets a user can have open", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "limit"}, {"name": "ticket settings", "description": "Configure the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket settings category", "description": "Configure the category where the tickets should open", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "topic, category"}, {"name": "ticket settings embed", "description": "Set a message to be sent when a member opens a ticket", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "topic, code"}, {"name": "ticket settings logs", "description": "Configure a channel for logging ticket transcripts", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "channel"}, {"name": "ticket settings reset", "description": "Disable the ticket module in the server", "permissions": "manage guild", "aliases": ["disable"], "category": "ticket", "arguments": "N/A"}, {"name": "ticket settings counting", "description": "Enable or disable the ticket counting system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "option"}, {"name": "ticket settings closed", "description": "Configure a category for closed tickets", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "category"}, {"name": "ticket unblacklist", "description": "Unblacklist a member from creating tickets", "permissions": "Manage guild", "aliases": [], "category": "ticket", "arguments": "user"}, {"name": "ticket info", "description": "View all tickets from a given user", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "user"}, {"name": "ticket topics", "description": "Manage the ticket topics", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket blacklisted", "description": "List all blacklisted users from creating tickets", "permissions": "Manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket status", "description": "Configure the ticket status", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket status list", "description": "List all ticket statuses", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket status add", "description": "Add a ticket status", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "name, category"}, {"name": "ticket status category", "description": "Change the category for a ticket status", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "name, category"}, {"name": "ticket status remove", "description": "Remove a ticket status", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket role", "description": "Manage the ticket roles", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket role set", "description": "Set a role as ticket owner role", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "role"}, {"name": "ticket role remove", "description": "Remove the ticket owner role", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket admin", "description": "Manage the members that can change the Antinuke settings", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket admin add", "description": "Give a user permissions to view ticket transcripts", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "member"}, {"name": "ticket admin remove", "description": "Remove a user's permissions to view ticket transcripts", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "member"}, {"name": "ticket topic", "description": "Manage the ticket topics", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket topic add", "description": "Create a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket topic weight", "description": "Edit a ticket topic weight", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name, weight"}, {"name": "ticket topic description", "description": "Edit a ticket topic description", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name, description"}, {"name": "ticket topic emoji", "description": "Edit a ticket topic emoji", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name, emoji"}, {"name": "ticket topic status", "description": "Edit a ticket topic status", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name, option"}, {"name": "ticket topic channelname", "description": "Edit a ticket topic channel name", "permissions": "administrator", "aliases": ["cn", "cname"], "category": "ticket", "arguments": "name, channelname"}, {"name": "ticket topic remove", "description": "Delete a ticket topic", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "name"}, {"name": "ticket topic channeltopic", "description": "Edit a ticket topic channel topic", "permissions": "administrator", "aliases": ["ct", "ctopic"], "category": "ticket", "arguments": "name, channeltopic"}, {"name": "ticket topic name", "description": "Edit a ticket topic name", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "old, new"}, {"name": "ticket support", "description": "Manage the support roles for the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket support remove", "description": "Remove a support role from the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "topic, role"}, {"name": "ticket support list", "description": "List all support roles for the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket support add", "description": "Add a support role to the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "topic, role"}, {"name": "ticket claim", "description": "Claim the current ticket", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket unclaim", "description": "Unclaim the current ticket", "permissions": "ticket support / manage channels", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket setup", "description": "Setup the ticket system", "permissions": "manage guild", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket allow", "description": "Add a user or role to the ticket", "permissions": "ticket support / manage channels", "aliases": ["add"], "category": "ticket", "arguments": "target"}, {"name": "ticket leaderboard", "description": "View the ticket leaderboard", "permissions": "N/A", "aliases": ["lb"], "category": "ticket", "arguments": "N/A"}, {"name": "ticket admins", "description": "View ticket admins on your server", "permissions": "administrator", "aliases": [], "category": "ticket", "arguments": "N/A"}, {"name": "ticket remind", "description": "Remind the ticket owner", "permissions": "ticket support / manage channels", "aliases": ["r"], "category": "ticket", "arguments": "reason"}, {"name": "tweet", "description": "Create a tweet", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "user, comment"}, {"name": "gend", "description": "End an active giveaway early", "permissions": "manage_server", "aliases": [], "category": "giveaway", "arguments": "message"}, {"name": "media", "description": "Media commands", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "media passed", "description": "Apply a passed filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "media wasted", "description": "Apply a wasted filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "media triggered", "description": "Apply a triggered filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "media jail", "description": "Apply a jail filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "media blur", "description": "Apply a blur filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "media gay", "description": "Apply a gay filter onto your photo", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "image"}, {"name": "suggestion", "description": "Suggestion commands", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "N/A"}, {"name": "suggestion disable", "description": "Disable the suggestion module", "permissions": "manage guild", "aliases": [], "category": "suggestion", "arguments": "N/A"}, {"name": "suggestion blacklist", "description": "Blacklist a member from creating suggestions", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "user, reason"}, {"name": "suggestion unblacklist", "description": "Unblacklist a member from creating suggestions", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "user"}, {"name": "suggestion blacklisted", "description": "List all blacklisted users from creating suggestions", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "N/A"}, {"name": "suggestion thread", "description": "Enable/Disable thread creation for suggestions", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "N/A"}, {"name": "suggestion channel", "description": "Set the suggestion channel", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "channel"}, {"name": "suggestion enable", "description": "Enable the suggestion module", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "N/A"}, {"name": "suggestion role", "description": "Set the suggestion role", "permissions": "Manage guild", "aliases": [], "category": "suggestion", "arguments": "role"}, {"name": "trueorfalse", "description": "Check if a statement is true or false", "permissions": "N/A", "aliases": ["tof"], "category": "fun", "arguments": "N/A"}, {"name": "shop", "description": "Manage you server economy shop", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop collect", "description": "Collect the earnings from the economy shop", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop remove", "description": "Remove a item from your economy shop", "permissions": "manage guild", "aliases": [], "category": "economy", "arguments": "role"}, {"name": "shop enable", "description": "Enable your server economy shop", "permissions": "manage guild", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop buy", "description": "Buy an item from the economy shop", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "shop disable", "description": "Disable your server economy shop", "permissions": "manage guild", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop view", "description": "Displays all items in the economy shop", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop balance", "description": "Check the current balance of the economy shop", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "shop add", "description": "Add a item to your economy shop", "permissions": "manage guild", "aliases": [], "category": "economy", "arguments": "role, amount, name"}, {"name": "g<PERSON><PERSON>", "description": "Reroll a winner for the specified giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, winners"}, {"name": "say", "description": "Send a message through the bot", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "channel, message"}, {"name": "set", "description": "Modify your server with evelina", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "set name", "description": "Change your server's name", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "name"}, {"name": "set icon", "description": "Change your server's icon", "permissions": "manage guild", "aliases": ["picture", "pic"], "category": "config", "arguments": "url"}, {"name": "set banner", "description": "Change your server's banner", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "url"}, {"name": "set splash", "description": "Change your server's splash", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "url"}, {"name": "ce", "description": "Create an embed using an embed code\n> **Embed Builder:** https://evelina.bot/embed", "permissions": "manage messages", "aliases": [], "category": "config", "arguments": "code"}, {"name": "lockdown", "description": "Manage lockdown settings", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "lockdown role", "description": "Manage lockdown role settings", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "lockdown role list", "description": "List all roles in the lockdown role list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "lockdown role remove", "description": "Remove a role from the lockdown role list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "role"}, {"name": "lockdown role add", "description": "Add a role to the lockdown role list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "role"}, {"name": "lockdown channel", "description": "Manage lockdown channel settings", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "lockdown channel list", "description": "List all channels in the lockdown channel list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "lockdown channel remove", "description": "Remove a channel from the lockdown channel list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "lockdown channel add", "description": "Add a channel to the lockdown channel list", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "lock", "description": "Lock a channel", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "store", "description": "View evelina online shop", "permissions": "N/A", "aliases": ["donation", "donator", "donate", "buy", "prices", "pricing"], "category": "store", "arguments": "N/A"}, {"name": "botclear", "description": "Delete messages sent by bots and messages starting with the command prefix", "permissions": "manage messages", "aliases": ["bc", "bp", "botpurge"], "category": "moderation", "arguments": "N/A"}, {"name": "unlock", "description": "Unlock a channel", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "blacklist", "description": "Blacklist commands", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "blacklist check", "description": "Check if a user or server is blacklisted and list all blacklists", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "target"}, {"name": "blacklist user", "description": "Blacklist a user permanently or temporarily", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "user, duration, reason"}, {"name": "blacklist cog", "description": "Blacklist a user or server from a cog permanently or temporarily", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "target, cog, duration, reason"}, {"name": "blacklist server", "description": "Blacklist a server permanently or temporarily", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "server, duration, reason"}, {"name": "blacklist command", "description": "Blacklist a user or server from a command permanently or temporarily", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "target, command, duration, reason"}, {"name": "reactionrole", "description": "Set up self-assignable roles with reactions", "permissions": "N/A", "aliases": ["rr"], "category": "autorole", "arguments": "N/A"}, {"name": "reactionrole list", "description": "View a list of every reaction role", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "reactionrole add", "description": "Adds a reaction role to a message", "permissions": "manage roles", "aliases": [], "category": "autorole", "arguments": "message, emoji, role"}, {"name": "reactionrole clear", "description": "Clears every reaction role from guild", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "reactionrole removeall", "description": "Removes all reaction roles from a message", "permissions": "manage roles", "aliases": [], "category": "autorole", "arguments": "message"}, {"name": "reactionrole remove", "description": "Removes a reaction role from a message", "permissions": "manage roles", "aliases": [], "category": "autorole", "arguments": "message, emoji"}, {"name": "usertrack", "description": "Start tracking usernames in a channel", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "usertrack add", "description": "Add a channel for username tracking", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "channel, length"}, {"name": "usertrack remove", "description": "Remove a channel for username tracking", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "usertrack name", "description": "Set a custom name for the username webhook", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "name"}, {"name": "usertrack avatar", "description": "Set a custom avatar for the username webhook", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "avatar"}, {"name": "withdraw", "description": "Withdraw card money to cash", "permissions": "N/A", "aliases": ["with"], "category": "economy", "arguments": "amount"}, {"name": "caption", "description": "Add a caption to an image", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "input_text"}, {"name": "company", "description": "Manage your company", "permissions": "N/A", "aliases": ["comp"], "category": "economy", "arguments": "N/A"}, {"name": "company name", "description": "Change your company's name", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "company privacy", "description": "Change your company's privacy settings", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "privacy"}, {"name": "company delete", "description": "Delete your company permanently", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company description", "description": "Change your company's description", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "description"}, {"name": "company kick", "description": "Kick a member from the company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company userinfo", "description": "Show information about a user in your company", "permissions": "N/A", "aliases": ["ui", "user"], "category": "economy", "arguments": "user"}, {"name": "company tag", "description": "Change your company's tag", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "tag"}, {"name": "company join", "description": "Join a company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "company create", "description": "Create a company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "tag, name"}, {"name": "company icon", "description": "Change your company's icon", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "icon"}, {"name": "company leave", "description": "Leave your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company transfer", "description": "Transfer the ownership of the company to another member", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company upgrade", "description": "Upgrade your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company request", "description": "Send a request to join a company or cancel an active request", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name, text"}, {"name": "company uprank", "description": "Promote a member within the company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company info", "description": "Show information about your company or another company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "company downrank", "description": "Demote a member within the company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company list", "description": "List all companys", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company project", "description": "Manage your company projects", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company project status", "description": "View the status of your company project", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company project cancel", "description": "Cancel your company project", "permissions": "N/A", "aliases": ["stop"], "category": "economy", "arguments": "N/A"}, {"name": "company project collect", "description": "Collect your company project earnings", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "company project contribute", "description": "Contribute to your company project", "permissions": "N/A", "aliases": ["cont"], "category": "economy", "arguments": "amount"}, {"name": "company project participants", "description": "View the participants of your company project", "permissions": "N/A", "aliases": ["part"], "category": "economy", "arguments": "N/A"}, {"name": "company project start", "description": "Start a project for your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "name"}, {"name": "company project complete", "description": "Complete your company project", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company project list", "description": "List all projects that exist", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company requests", "description": "View all pending join requests for your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company members", "description": "Manage your company members", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company members list", "description": "List all members of your company with their ranks", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company members stats", "description": "List all members of your company with their stats", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company invite", "description": "Invite a user to join your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company vault", "description": "Manage your company vault", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company vault bonus", "description": "Send a bonus to a user from your company vault", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user, amount"}, {"name": "company vault logs", "description": "View the company vault logs", "permissions": "N/A", "aliases": ["history"], "category": "economy", "arguments": "N/A"}, {"name": "company vault withdraw", "description": "Withdraw cash from your company vault", "permissions": "N/A", "aliases": ["with"], "category": "economy", "arguments": "amount"}, {"name": "company vault limit", "description": "Set the company vault limit", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "company vault deposit", "description": "Deposit cash into your company vault", "permissions": "N/A", "aliases": ["dep"], "category": "economy", "arguments": "amount"}, {"name": "company uninvite", "description": "Uninvite a user from joining your company", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "company leaderboard", "description": "View the company leaderboard", "permissions": "N/A", "aliases": ["lb"], "category": "economy", "arguments": "N/A"}, {"name": "company leaderboard votes", "description": "View the company leaderboard by votes", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company leaderboard vault", "description": "View the company leaderboard by vault", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "company leaderboard networth", "description": "View the company leaderboard by networth", "permissions": "N/A", "aliases": ["nw"], "category": "economy", "arguments": "N/A"}, {"name": "company leaderboard reputation", "description": "View the company leaderboard by reputation", "permissions": "N/A", "aliases": ["rep"], "category": "economy", "arguments": "N/A"}, {"name": "lick", "description": "Lick a member", "permissions": "N/A", "aliases": ["slurp"], "category": "roleplay", "arguments": "member"}, {"name": "imageonly", "description": "Let members only send images in channels", "permissions": "manage channels", "aliases": ["imgonly", "gallery"], "category": "config", "arguments": "N/A"}, {"name": "imageonly add", "description": "Add an image only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "imageonly remove", "description": "Remove an image only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "imageonly list", "description": "Returns a list of all image only channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "vanitytrack", "description": "Start tracking vanitys in a channel", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "vanitytrack name", "description": "Set a custom name for the vanity webhook", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "name"}, {"name": "vanitytrack remove", "description": "Remove a channel for vanity tracking", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "vanitytrack avatar", "description": "Set a custom avatar for the vanity webhook", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "avatar"}, {"name": "vanitytrack add", "description": "Add a channel for vanity tracking", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "channel, length"}, {"name": "kiss", "description": "Kiss a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "deposit", "description": "Deposit cash money to card", "permissions": "N/A", "aliases": ["dep"], "category": "economy", "arguments": "amount"}, {"name": "blacktea", "description": "Play blacktea with the server members", "permissions": "N/A", "aliases": ["bt"], "category": "fun", "arguments": "N/A"}, {"name": "blacktea end", "description": "End the blacktea game", "permissions": "N/A", "aliases": ["stop"], "category": "fun", "arguments": "N/A"}, {"name": "fuck", "description": "Fuck a member", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "botonly", "description": "Let only bots send in channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "botonly add", "description": "Add an bot only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "botonly remove", "description": "Remove an bot only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "botonly list", "description": "Returns a list of all bot only channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "rockpaperscissors", "description": "Play rock-paper-scissors with a member or the bot", "permissions": "N/A", "aliases": ["rps"], "category": "fun", "arguments": "opponent"}, {"name": "anal", "description": "Fuck a member anal", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "unblacklist", "description": "Unblacklist commands", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "unblacklist server", "description": "Unblacklist a server", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "server"}, {"name": "unblacklist command", "description": "Unblacklist a user or server from a command", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "target, command"}, {"name": "unblacklist cog", "description": "Unblacklist a user or server from a cog", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "target, cog"}, {"name": "unblacklist user", "description": "Unblacklist a user", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "user"}, {"name": "blowjob", "description": "<PERSON><PERSON><PERSON><PERSON> a member", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "tict<PERSON><PERSON>", "description": "Play tictactoe with a member", "permissions": "N/A", "aliases": ["ttt"], "category": "fun", "arguments": "member"}, {"name": "cum", "description": "Cum on a member", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "react", "description": "Add a reaction to a message", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "message, emoji"}, {"name": "pussylick", "description": "<PERSON><PERSON><PERSON><PERSON> a member", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "guessthenumner", "description": "Guess the number game", "permissions": "N/A", "aliases": ["gtn"], "category": "fun", "arguments": "N/A"}, {"name": "guessthenumner list", "description": "List all guess the number games", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "guessthenumner start", "description": "Start a guess the number game", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "channel, min, max"}, {"name": "guessthenumner lock", "description": "Change the lock status of the game", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "mode"}, {"name": "guessthenumner stop", "description": "Stop the guess the number game", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "channel"}, {"name": "balance", "description": "Check someone's balance and rank", "permissions": "N/A", "aliases": ["bal"], "category": "economy", "arguments": "member"}, {"name": "youtube", "description": "Manage YouTube notifications", "permissions": "N/A", "aliases": [], "category": "youtube", "arguments": "N/A"}, {"name": "youtube add", "description": "Enable post notifications for a channel", "permissions": "manage server", "aliases": [], "category": "youtube", "arguments": "username, channel, message"}, {"name": "youtube remove", "description": "Disable post notifications for a channel", "permissions": "manage server", "aliases": [], "category": "youtube", "arguments": "username"}, {"name": "youtube list", "description": "View all YouTube post notifications", "permissions": "manage server", "aliases": [], "category": "youtube", "arguments": "N/A"}, {"name": "youtube check", "description": "Get the latest video from a channel", "permissions": "N/A", "aliases": [], "category": "youtube", "arguments": "username"}, {"name": "threesome", "description": "Threesome commands", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "N/A"}, {"name": "threesome ffm", "description": "Threesome with two girls & one boy", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member, partner"}, {"name": "threesome fmm", "description": "Threesome with one girl & two boys", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member, partner"}, {"name": "threesome fff", "description": "Threesome with only girls", "permissions": "NSFW Channel", "aliases": [], "category": "roleplay", "arguments": "member, partner"}, {"name": "transfer", "description": "Transfer cash to a member", "permissions": "N/A", "aliases": ["give", "pay"], "category": "economy", "arguments": "member, amount"}, {"name": "flags", "description": "Rate your knowledge of flags", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "difficulty, mode"}, {"name": "guildleaderboard", "description": "Guild leaderboard for the economy", "permissions": "N/A", "aliases": ["glb"], "category": "economy", "arguments": "N/A"}, {"name": "leaderboard", "description": "Global leaderboard for the economy", "permissions": "N/A", "aliases": ["lb"], "category": "economy", "arguments": "N/A"}, {"name": "dice", "description": "Play a dice game", "permissions": "N/A", "aliases": ["gamble"], "category": "economy", "arguments": "amount"}, {"name": "textonly", "description": "Let no bot commands working in channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "textonly add", "description": "Add a text only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "textonly remove", "description": "Remove a text only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "textonly list", "description": "Returns a list of all text only channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "cashleaderboard", "description": "Cash leaderboard for the economy", "permissions": "N/A", "aliases": ["clb"], "category": "economy", "arguments": "N/A"}, {"name": "prefix", "description": "Manage prefixes for the server", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "prefix remove", "description": "Remove command prefix for server", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "prefix set", "description": "Set command prefix for server", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "prefix"}, {"name": "prefix leaderboard", "description": "View the leaderboard of prefixes used for the bot", "permissions": "N/A", "aliases": ["lb"], "category": "config", "arguments": "N/A"}, {"name": "networthleaderboard", "description": "Networth leaderboard for the economy", "permissions": "N/A", "aliases": ["nlb"], "category": "economy", "arguments": "N/A"}, {"name": "autoreact", "description": "Add a reaction(s) to a message", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoreact remove", "description": "Removes a reaction trigger in guild", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "trigger"}, {"name": "autoreact add", "description": "Adds a reaction trigger to guild", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "content"}, {"name": "autoreact reset", "description": "Reset all reaction triggers in guild", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoreact channel", "description": "Set up autoreact channel", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoreact channel add", "description": "Add a channel to autoreact", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "channel, reactions"}, {"name": "autoreact channel remove", "description": "Remove a channel from autoreact", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "channel"}, {"name": "autoreact channel list", "description": "View a list of every channel in autoreact", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoreact channel reset", "description": "Reset all autoreact channels in guild", "permissions": "manage guild", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "autoreact list", "description": "View a list of every reaction trigger in guild", "permissions": "N/A", "aliases": [], "category": "responders", "arguments": "N/A"}, {"name": "hide", "description": "Hide a channel from everyone", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "roulette", "description": "Play a roulette game", "permissions": "N/A", "aliases": ["ro"], "category": "economy", "arguments": "amount, bet"}, {"name": "slowmode", "description": "Enable slowmode option in a text channel", "permissions": "manage channels", "aliases": ["cooldown"], "category": "moderation", "arguments": "time, channel"}, {"name": "ladder", "description": "Start a ladder game", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "giveaway", "description": "Manage giveaways in your server", "permissions": "N/A", "aliases": ["gw"], "category": "giveaway", "arguments": "N/A"}, {"name": "giveaway delete", "description": "Delete a giveaway", "permissions": "manage server", "aliases": [], "category": "giveaway", "arguments": "message_id"}, {"name": "giveaway reroll", "description": "Reroll a specified number of winners for the giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, winners"}, {"name": "giveaway end", "description": "End an active giveaway early", "permissions": "manage_server", "aliases": [], "category": "giveaway", "arguments": "message"}, {"name": "giveaway requirements", "description": "Manage a giveaway's requirements", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "N/A"}, {"name": "giveaway requirements edit", "description": "Edit an existing requirement in the giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, flag, input"}, {"name": "giveaway requirements remove", "description": "Remove a requirement from the giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, flag"}, {"name": "giveaway requirements add", "description": "Add a new requirement to the giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, flag, input"}, {"name": "giveaway create", "description": "Start a giveaway with your provided duration, winners and prize description", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "channel, time, winners, prize"}, {"name": "giveaway list", "description": "List every active giveaway in the server", "permissions": "N/A", "aliases": [], "category": "giveaway", "arguments": "N/A"}, {"name": "giveaway edit", "description": "Edit a giveaway's settings", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "N/A"}, {"name": "giveaway edit duration", "description": "Edit the duration of a giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, duration"}, {"name": "giveaway edit host", "description": "Edit the host of a giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, host"}, {"name": "giveaway edit winners", "description": "Edit the number of winners of a giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, winners"}, {"name": "giveaway edit price", "description": "Edit the prize of a giveaway", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "message, price"}, {"name": "coinflip", "description": "Play a coinflip game", "permissions": "N/A", "aliases": ["cf"], "category": "economy", "arguments": "amount, bet"}, {"name": "blackjack", "description": "Start a blackjack game", "permissions": "N/A", "aliases": ["bj"], "category": "economy", "arguments": "amount"}, {"name": "pinch", "description": "Pinch a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "mines", "description": "Start a mines game", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount, count"}, {"name": "cry", "description": "Start crying", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "N/A"}, {"name": "higherlower", "description": "Guess if the number is higher or lower", "permissions": "N/A", "aliases": ["hl"], "category": "economy", "arguments": "amount"}, {"name": "slot", "description": "Play a slot machine game", "permissions": "N/A", "aliases": ["slots"], "category": "economy", "arguments": "amount"}, {"name": "work", "description": "Work a job and earn money", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "cuddle", "description": "<PERSON>uddle a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "unhide", "description": "Unhide a channel from everyone", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "channel"}, {"name": "gcreate", "description": "Start a giveaway with your provided duration, winners and prize description", "permissions": "manage guild", "aliases": [], "category": "giveaway", "arguments": "channel, time, winners, prize"}, {"name": "crash", "description": "Start a crash game", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "minigames", "description": "View all available economy games", "permissions": "N/A", "aliases": ["games"], "category": "economy", "arguments": "N/A"}, {"name": "hug", "description": "Hug a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "mute", "description": "Timeout a member", "permissions": "moderate members", "aliases": ["timeout"], "category": "moderation", "arguments": "member, time, reason"}, {"name": "cooldowns", "description": "Check the cooldowns for all economy commands", "permissions": "N/A", "aliases": ["cd"], "category": "economy", "arguments": "member"}, {"name": "bonus", "description": "Claim your bonus cash", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "ban", "description": "Ban a member from the server", "permissions": "ban members", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "linkonly", "description": "Let members only send links in channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "linkonly add", "description": "Add a link only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "linkonly remove", "description": "Remove a link only channel", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "linkonly list", "description": "Returns a list of all link only channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "beg", "description": "Beg for money", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "pat", "description": "<PERSON> a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "slut", "description": "Work as a prostitute for money", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "rob", "description": "Attempt to steal money from another player", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "member"}, {"name": "unmute", "description": "Remove the timeout from a member", "permissions": "moderate members", "aliases": ["untimeout"], "category": "moderation", "arguments": "member, reason"}, {"name": "daily", "description": "Claim your daily cash", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "slap", "description": "Slap a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "dailystreak", "description": "Get your daily streak with a calendar", "permissions": "N/A", "aliases": ["dstreak", "ds"], "category": "economy", "arguments": "user"}, {"name": "setmute", "description": "Set up mute module", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "laugh", "description": "Start laughing", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "N/A"}, {"name": "unsetmute", "description": "Unset the mute module", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "economystats", "description": "Get your economy stats", "permissions": "N/A", "aliases": ["ecostats"], "category": "economy", "arguments": "member"}, {"name": "counting", "description": "Counting game commands", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "counting remove", "description": "Remove the counting channel", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "counting leaderboard", "description": "Displays the highest number counts across servers", "permissions": "N/A", "aliases": ["lb", "top"], "category": "fun", "arguments": "N/A"}, {"name": "counting safemode", "description": "Enable or disable safe mode for the counting game", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "mode"}, {"name": "counting set", "description": "Set the counting channel", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "channel"}, {"name": "stfu", "description": "Tell a member to shut up", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "imute", "description": "Mute a member from sending images", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "member, channel"}, {"name": "commands", "description": "View all commands or get help for a specific command", "permissions": "N/A", "aliases": ["h", "cmds"], "category": "info", "arguments": "command"}, {"name": "marry", "description": "Marry a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member"}, {"name": "iunmute", "description": "Unmute a member from sending images", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "member, channel"}, {"name": "collectall", "description": "Collect all available earnings from your business, laboratory, daily, beg, bonus, work, slut, and company projects", "permissions": "donator", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "ship", "description": "Check the ship rate between you and a member", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member, partner"}, {"name": "rmute", "description": "Mute a member from reacting to messages", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "member, channel"}, {"name": "item", "description": "View your economy profile", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "item sell", "description": "Sell items from your account", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "item sell bank", "description": "Sell bank space", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "item buy", "description": "Buy items for your account", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "item buy bank", "description": "Buy additional bank space", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "amount"}, {"name": "payment", "description": "Manage payment methods", "permissions": "N/A", "aliases": [], "category": "reseller", "arguments": "N/A"}, {"name": "payment add", "description": "Add a payment method to guild", "permissions": "manage guild", "aliases": ["create"], "category": "reseller", "arguments": "method, receiver"}, {"name": "payment edit", "description": "Edit a payment method", "permissions": "manage guild", "aliases": [], "category": "reseller", "arguments": "method, receiver"}, {"name": "payment reset", "description": "Reset every payment method for this guild", "permissions": "manage guild", "aliases": [], "category": "reseller", "arguments": "N/A"}, {"name": "payment list", "description": "View a list of every payment method in guild", "permissions": "manage guild", "aliases": [], "category": "reseller", "arguments": "N/A"}, {"name": "payment remove", "description": "Remove a payment method from guild", "permissions": "manage guild", "aliases": ["delete", "del"], "category": "reseller", "arguments": "method"}, {"name": "payment currency", "description": "Set the currency for the guild", "permissions": "manage guild", "aliases": [], "category": "reseller", "arguments": "currency"}, {"name": "shards", "description": "Check status of each bot shard", "permissions": "N/A", "aliases": [], "category": "info", "arguments": "N/A"}, {"name": "married", "description": "Create a marriage certificate", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "member, partner"}, {"name": "runmute", "description": "Unmute a member from reacting to messages", "permissions": "moderate members", "aliases": [], "category": "moderation", "arguments": "member, channel"}, {"name": "marriage", "description": "View your marriage or from a given user", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "user"}, {"name": "kick", "description": "Kick a member from the server", "permissions": "kick members", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "variables", "description": "View all available variables for embed messages", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "enablecmd", "description": "Enable a command in the server or a specific channel.", "permissions": "manage guild", "aliases": ["ecmd"], "category": "config", "arguments": "scope, command"}, {"name": "ping", "description": "Displays the bot's latency", "permissions": "N/A", "aliases": [], "category": "info", "arguments": "N/A"}, {"name": "disablecmd", "description": "Disable a command in the server or a specific channel.", "permissions": "manage guild", "aliases": ["dcmd"], "category": "config", "arguments": "scope, command"}, {"name": "divorce", "description": "Divorce from your partner", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "N/A"}, {"name": "botinfo", "description": "Displays information about the bot", "permissions": "N/A", "aliases": ["bi", "bot", "about"], "category": "info", "arguments": "N/A"}, {"name": "glist", "description": "List every active giveaway in the server", "permissions": "N/A", "aliases": [], "category": "giveaway", "arguments": "N/A"}, {"name": "invite", "description": "Send an invite link of the bot", "permissions": "N/A", "aliases": ["link"], "category": "info", "arguments": "N/A"}, {"name": "unban", "description": "Unban a member from the server", "permissions": "ban members", "aliases": [], "category": "moderation", "arguments": "user, reason"}, {"name": "edater", "description": "List all married users", "permissions": "N/A", "aliases": [], "category": "roleplay", "arguments": "N/A"}, {"name": "fakepermissions", "description": "Set up fake permissions for role through the bot", "permissions": "N/A", "aliases": ["fakeperms", "fp"], "category": "config", "arguments": "N/A"}, {"name": "fakepermissions add", "description": "<PERSON> a fake permission to a role", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "role, permission"}, {"name": "fakepermissions list", "description": "List all fake permissions", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "role"}, {"name": "fakepermissions perms", "description": "Get a list of valid permissions for the server", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "fakepermissions reset", "description": "Reset all fake permissions for the server", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "fakepermissions remove", "description": "Remove a fake permission from a role", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "role, permission"}, {"name": "fakepermissions clear", "description": "Clear all fake permissions for a role", "permissions": "administrator", "aliases": [], "category": "config", "arguments": "role"}, {"name": "unbanall", "description": "Unban all members from the server", "permissions": "ban members", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "disablemodule", "description": "Disable a module in the server or a specific channel.", "permissions": "manage guild", "aliases": ["dmodule"], "category": "config", "arguments": "scope, module"}, {"name": "getbotinvite", "description": "Get the bot invite based on it's id", "permissions": "N/A", "aliases": ["gbi"], "category": "info", "arguments": "member"}, {"name": "nickname", "description": "Change a member's nickname", "permissions": "manage nicknames", "aliases": ["nick"], "category": "moderation", "arguments": "member, nick"}, {"name": "disabledcmds", "description": "View all disabled commands in the server", "permissions": "Manage guild", "aliases": ["dcmds"], "category": "config", "arguments": "N/A"}, {"name": "enablemodule", "description": "Enable a module in the server or a specific channel.", "permissions": "manage guild", "aliases": ["emodule"], "category": "config", "arguments": "scope, module"}, {"name": "strip", "description": "Remove someone's dangerous roles, ignoring bot-managed roles", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "autopublish", "description": "Manage autopublish channels", "permissions": "manage channels", "aliases": ["ap"], "category": "config", "arguments": "N/A"}, {"name": "autopublish add", "description": "Add a channel to autopublish", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "autopublish remove", "description": "Remove a channel from autopublish", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "autopublish list", "description": "List all autopublish channels", "permissions": "manage channels", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "card", "description": "View your economy cards", "permissions": "N/A", "aliases": ["cards"], "category": "economy", "arguments": "N/A"}, {"name": "card unuse", "description": "Unuse a card from your account", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "card_id"}, {"name": "card used", "description": "View all your used cards", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user"}, {"name": "card sell", "description": "Sell a card from your account", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "card_id, member, amount"}, {"name": "card upgrade", "description": "Upgrade your cards", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "stars, type"}, {"name": "card list", "description": "View all your active cards with filters and pagination", "permissions": "N/A", "aliases": ["view"], "category": "economy", "arguments": "user"}, {"name": "card shred", "description": "Shred cards to gain money, type is either `stars` or `id`", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "type, id, amount"}, {"name": "card generate", "description": "Generate a card", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "user, card_id, type"}, {"name": "card case", "description": "View your economy cases", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "N/A"}, {"name": "card case buy", "description": "Buy a case to get a random item", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "type, amount"}, {"name": "card case open", "description": "Open cases to get random items", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "case, type, amount"}, {"name": "card trade", "description": "Trade a card with another user", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "card_id, target_card_id, member"}, {"name": "card use", "description": "Use a card from your account", "permissions": "N/A", "aliases": [], "category": "economy", "arguments": "card_id"}, {"name": "lookup", "description": "Lookup commands", "permissions": "donator", "aliases": ["pomelo", "handles"], "category": "donor", "arguments": "N/A"}, {"name": "lookup username", "description": "Get the most recent username changes", "permissions": "donator", "aliases": ["usernames"], "category": "donor", "arguments": "length"}, {"name": "lookup vanity", "description": "Get the most recent vanity changes", "permissions": "donator", "aliases": ["vanitys"], "category": "donor", "arguments": "length"}, {"name": "uptime", "description": "Displays how long has the bot been online for", "permissions": "N/A", "aliases": ["up"], "category": "info", "arguments": "N/A"}, {"name": "warn", "description": "N/A", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "warn all", "description": "Returns all warns on the server", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn rewards", "description": "Manage the roles that get a user for certains warns", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn rewards sync", "description": "Sync all user's roles based on their warns", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn rewards add", "description": "Assign a warn role to a warn level", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "warn, role"}, {"name": "warn rewards remove", "description": "Remove a reward from a warn level", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "warn"}, {"name": "warn rewards list", "description": "Get a list of every role reward in this server", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn rewards reset", "description": "Remove every reward that was added", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn clear", "description": "Clear all warns from an user", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "warn reset", "description": "Reset all warns from your server", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn remove", "description": "Remove a specific warn by its number", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "member, warn, reason"}, {"name": "warn punishment", "description": "Manage punishments that get a user for certains warns", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn punishment add", "description": "Assign a punishment to a warn level", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "warn, action, time"}, {"name": "warn punishment list", "description": "Get a list of every punishment in this server", "permissions": "Manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn punishment remove", "description": "Remove a punishment from a warn level", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "warn"}, {"name": "warn punishment reset", "description": "Remove every punishment that was added", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "warn list", "description": "Returns all warns that a user has", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "View all disabled modules in the server", "permissions": "Manage guild", "aliases": ["dmodules"], "category": "config", "arguments": "N/A"}, {"name": "role", "description": "Add/remove roles to/from a member", "permissions": "manage roles", "aliases": ["r"], "category": "role", "arguments": "member, roles"}, {"name": "role delete", "description": "Delete an existing role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role"}, {"name": "role all", "description": "Add or remove roles from all members", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "N/A"}, {"name": "role all add", "description": "Add a role to all members of a specified type (<PERSON>, <PERSON><PERSON>, Members)", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "type, role"}, {"name": "role all remove", "description": "Remove a role from all members of a specified type (<PERSON>, <PERSON><PERSON>, Members)", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "type, role"}, {"name": "role copy", "description": "<PERSON>py a role's permissions to another role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "N/A"}, {"name": "role copy color", "description": "Copy a role's color to another role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, target"}, {"name": "role copy name", "description": "<PERSON><PERSON> a role's name to another role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, target"}, {"name": "role copy all", "description": "Copy a role's name, color, permissions, hoist, icon, and more to another role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, target"}, {"name": "role copy permissions", "description": "<PERSON>py a role's permissions to another role", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, target"}, {"name": "role create", "description": "Create a new role with an optional colour", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "name, colour"}, {"name": "role manage", "description": "Add/remove roles to/from a member", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "member, roles"}, {"name": "role edit", "description": "Edit a role's name, icon & color", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "N/A"}, {"name": "role edit name", "description": "Edit a role's name", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, name"}, {"name": "role edit icon", "description": "Edit a role's icon", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role, emoji"}, {"name": "role edit hoist", "description": "Make a role hoisted or not", "permissions": "manage roles", "aliases": [], "category": "role", "arguments": "role"}, {"name": "role edit color", "description": "Edit a role's color", "permissions": "manage roles", "aliases": ["colour"], "category": "role", "arguments": "role, color"}, {"name": "jail", "description": "Restrict someone from the server's channels", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "member, time, reason"}, {"name": "voicetrack", "description": "Shows how long a users' been in a voice channel", "permissions": "N/A", "aliases": ["vct", "vt"], "category": "voicetrack", "arguments": "member"}, {"name": "voicetrack reset", "description": "Reset the voicetracking system in your server", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack leaderboard", "description": "List the members with the most voice time", "permissions": "N/A", "aliases": ["lb"], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack clear", "description": "Clear your voice tracking data for the current guild", "permissions": "manage guild", "aliases": ["c"], "category": "voicetrack", "arguments": "member"}, {"name": "voicetrack disablemute", "description": "Disable the mute time tracking", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack globalleaderboard", "description": "List the members with the most voice time over all servers", "permissions": "N/A", "aliases": ["glb"], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack enable", "description": "Enable the voice track feature for your guild", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack leveling", "description": "Manage voice leveling", "permissions": "N/A", "aliases": ["level"], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack leveling disable", "description": "Disable the voice leveling feature for your guild", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack leveling enable", "description": "Enable the voice leveling feature for your guild", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack disable", "description": "Disable the voice track feature for your guild", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "voicetrack enablemute", "description": "Enable the mute time tracking", "permissions": "manage guild", "aliases": [], "category": "voicetrack", "arguments": "N/A"}, {"name": "automod", "description": "Manage automod settings for your server", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod invites", "description": "Prevent members from sending invite links", "permissions": "manage guild", "aliases": ["invite"], "category": "automod", "arguments": "N/A"}, {"name": "automod invites enable", "description": "Enable protection against invite links", "permissions": "manage guild", "aliases": ["on"], "category": "automod", "arguments": "N/A"}, {"name": "automod invites message", "description": "Set a custom message for invites filter", "permissions": "manage guild", "aliases": ["msg"], "category": "automod", "arguments": "message"}, {"name": "automod invites disable", "description": "Disable the invites filter", "permissions": "manage guild", "aliases": ["off"], "category": "automod", "arguments": "N/A"}, {"name": "automod invites logs", "description": "Set the channel where logs will be sent for invites filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "channel"}, {"name": "automod invites timeout", "description": "Change timeout duration for members who send invite links", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "time"}, {"name": "automod invites ignore", "description": "Manage ignored channels and roles for invites filter", "permissions": "manage guild", "aliases": ["exempt"], "category": "automod", "arguments": "N/A"}, {"name": "automod invites ignore add", "description": "Add a user, role or channel to the antispam whitelist", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod invites ignore list", "description": "List ignored users, channels and roles for invites filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod invites ignore remove", "description": "Remove ignored users, channels and roles for invites filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod spam", "description": "Prevent members from spamming messages", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod spam rate", "description": "Change rate at which members can sending messages in 10 seconds before triggering anti-spam protection", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "rate"}, {"name": "automod spam timeout", "description": "Change timeout duration for members who spam messages", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "time"}, {"name": "automod spam enable", "description": "Enable protection against message spamming", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod spam disable", "description": "Disable protection against message spamming", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod spam message", "description": "Enable or disable the antispam punishment message", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "option"}, {"name": "automod spam ignore", "description": "Manage ignored channels and roles for antispam", "permissions": "manage guild", "aliases": ["exempt"], "category": "automod", "arguments": "N/A"}, {"name": "automod spam ignore list", "description": "List ignored users, channels and roles for antispam", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod spam ignore remove", "description": "Remove ignored users, channels and roles for antispam", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod spam ignore add", "description": "Add a user, role or channel to the antispam whitelist", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod words", "description": "Prevent members from using blacklisted words", "permissions": "manage guild", "aliases": ["word"], "category": "automod", "arguments": "N/A"}, {"name": "automod words timeout", "description": "Change timeout duration for members who spam messages", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "time"}, {"name": "automod words add", "description": "Add a blacklisted word to the words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "word"}, {"name": "automod words enable", "description": "Enable protection against blacklisted words", "permissions": "manage guild", "aliases": ["on"], "category": "automod", "arguments": "N/A"}, {"name": "automod words clear", "description": "Clear all blacklisted words for words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod words remove", "description": "Remove a blacklisted word from the words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "word"}, {"name": "automod words message", "description": "Set a custom message for words filter", "permissions": "manage guild", "aliases": ["msg"], "category": "automod", "arguments": "message"}, {"name": "automod words list", "description": "List blacklisted words for words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod words ignore", "description": "Manage ignored channels and roles for words filter", "permissions": "manage guild", "aliases": ["exempt"], "category": "automod", "arguments": "N/A"}, {"name": "automod words ignore remove", "description": "Remove ignored users, channels and roles for antispam", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod words ignore list", "description": "List ignored users, channels and roles for words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod words ignore add", "description": "Add a user, role or channel to the antispam whitelist", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod words disable", "description": "Disable the words filter", "permissions": "manage guild", "aliases": ["off"], "category": "automod", "arguments": "N/A"}, {"name": "automod words logs", "description": "Set the channel where logs will be sent for words filter", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "channel"}, {"name": "automod repeat", "description": "Prevent members from repeating messages", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod repeat ignore", "description": "Manage ignored channels and roles for antirepeat", "permissions": "manage guild", "aliases": ["exempt"], "category": "automod", "arguments": "N/A"}, {"name": "automod repeat ignore add", "description": "Add a user, role or channel to the antirepeat whitelist", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod repeat ignore list", "description": "List all ignored users, channels and roles for antirepeat", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod repeat ignore remove", "description": "Remove a user, role or channel from the antirepeat whitelist", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "target"}, {"name": "automod repeat rate", "description": "Change rate at which members can sending messages in 10 seconds before triggering anti-repeat protection", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "rate"}, {"name": "automod repeat timeout", "description": "Change timeout duration for members who repeat messages", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "time"}, {"name": "automod repeat message", "description": "Enable or disable the antirepeat punishment message", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "option"}, {"name": "automod repeat disable", "description": "Disable protection against message repeating", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "automod repeat enable", "description": "Enable protection against message repeating", "permissions": "manage guild", "aliases": [], "category": "automod", "arguments": "N/A"}, {"name": "topcommands", "description": "View the top 50 most used commands", "permissions": "N/A", "aliases": ["topcmds"], "category": "info", "arguments": "time"}, {"name": "stats", "description": "Check a member's stats for a certain game", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "stats blacktea", "description": "View a member's stats for blacktea", "permissions": "N/A", "aliases": ["bt"], "category": "fun", "arguments": "user"}, {"name": "stats leaderboard", "description": "Get the leaderboard for a specific game", "permissions": "N/A", "aliases": ["lb"], "category": "fun", "arguments": "game"}, {"name": "stats flags", "description": "View a member's stats for flags", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "user"}, {"name": "stats tictactoe", "description": "View a member's stats for tictactoe", "permissions": "N/A", "aliases": ["ttt"], "category": "fun", "arguments": "user"}, {"name": "stats rockpaperscissors", "description": "View a member's stats for rockpaperscissors", "permissions": "N/A", "aliases": ["rps"], "category": "fun", "arguments": "user"}, {"name": "donor", "description": "Donor commands", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "N/A"}, {"name": "donor add", "description": "Add donator perks to a member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "member"}, {"name": "donor remove", "description": "Remove donator perks from a member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "member"}, {"name": "webhook", "description": "Set up webhooks in your server", "permissions": "N/A", "aliases": [], "category": "webhooks", "arguments": "N/A"}, {"name": "webhook send", "description": "Send a message to an existing channel webhook.", "permissions": "manage webhooks", "aliases": [], "category": "webhooks", "arguments": "code, script"}, {"name": "webhook create", "description": "Create webhook to forward messages to", "permissions": "manage webhooks", "aliases": [], "category": "webhooks", "arguments": "channel, name"}, {"name": "webhook list", "description": "List all available webhooks in the server", "permissions": "N/A", "aliases": [], "category": "webhooks", "arguments": "N/A"}, {"name": "webhook delete", "description": "Delete webhook for a channel", "permissions": "manage webhooks", "aliases": [], "category": "webhooks", "arguments": "code"}, {"name": "webhook edit", "description": "Edit the webhook's look", "permissions": "manage webhooks", "aliases": [], "category": "webhooks", "arguments": "N/A"}, {"name": "webhook edit avatar", "description": "Edit the webhook's avatar", "permissions": "manage webhooks", "aliases": ["icon"], "category": "webhooks", "arguments": "code, url"}, {"name": "webhook edit name", "description": "Edit a webhook's name", "permissions": "manage webhooks", "aliases": [], "category": "webhooks", "arguments": "code, name"}, {"name": "restore", "description": "Give a member their roles back after rejoining", "permissions": "manage roles", "aliases": [], "category": "moderation", "arguments": "member"}, {"name": "reposter", "description": "Fine tune reposts which can be used in your server", "permissions": "N/A", "aliases": ["rp"], "category": "config", "arguments": "N/A"}, {"name": "reposter prefix", "description": "Change the prefix for reposts", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "prefix"}, {"name": "reposter embed", "description": "Enable or disable embeds for reposts", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "option"}, {"name": "reposter delete", "description": "Enable or disable deletion of trigger messages", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "option"}, {"name": "reposter channel", "description": "Add or remove a channel for reposts blacklist", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "channel"}, {"name": "reposter status", "description": "Enable or disable reposts in your server", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "option"}, {"name": "paginate", "description": "Create and manage paginated messages", "permissions": "N/A", "aliases": [], "category": "paginate", "arguments": "N/A"}, {"name": "paginate move", "description": "Swaps the embeds between two pages", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "message, page, new"}, {"name": "paginate add", "description": "Adds an embed to the next available page without gaps", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "message, embed"}, {"name": "paginate remove", "description": "Removes an embed from a specified page, shifting pages to close gaps", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "message, page"}, {"name": "paginate edit", "description": "Edits an embed on a specified page", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "message, page, embed"}, {"name": "paginate reset", "description": "Removes all paginated messages", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "N/A"}, {"name": "paginate clear", "description": "Removes all embeds from a message", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "message"}, {"name": "paginate list", "description": "Lists all paginated messages in the guild", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "N/A"}, {"name": "paginate create", "description": "Creates a new paginated message starting with the first page", "permissions": "manage messages", "aliases": [], "category": "paginate", "arguments": "embed"}, {"name": "bump<PERSON>inder", "description": "Get reminders to /bump your server on Disboard!", "permissions": "N/A", "aliases": ["bump"], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder disable", "description": "Disable the disboard bump reminder feature", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "<PERSON><PERSON><PERSON> thankyou", "description": "Set the 'Thank You' message for successfully running /bump", "permissions": "manage guild", "aliases": ["ty"], "category": "config", "arguments": "code"}, {"name": "bumpreminder view", "description": "View your bump reminder/thankyou messages", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder view reminder", "description": "View your bump reminder message", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder view thankyou", "description": "View your bump thankyou message", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder test", "description": "Test your bump reminder/thankyou messages", "permissions": "N/A", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder test reminder", "description": "Test your bump reminder message", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder test thankyou", "description": "Test your bump thankyou message", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder leaderboard", "description": "View the bump reminder leaderboard", "permissions": "N/A", "aliases": ["lb"], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder enable", "description": "Enable the disboard bump reminder feature in your server", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "N/A"}, {"name": "bumpreminder reminder", "description": "Set the reminder message to run /bump", "permissions": "manage guild", "aliases": [], "category": "config", "arguments": "code"}, {"name": "suggest", "description": "Submit a suggestion", "permissions": "N/A", "aliases": [], "category": "suggestion", "arguments": "content"}, {"name": "unsetjail", "description": "Disable the jail module", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "bug", "description": "<PERSON><PERSON> commands", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "N/A"}, {"name": "bug list", "description": "List all bugs from a given member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "member"}, {"name": "bug leaderboard", "description": "Display a leaderboard of members with the most bug reports", "permissions": "bot supporter", "aliases": ["lb"], "category": "supporter", "arguments": "N/A"}, {"name": "bug add", "description": "Add a bug report to a member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "member, reason"}, {"name": "bug remove", "description": "Remove a bug report from a member", "permissions": "bot supporter", "aliases": [], "category": "supporter", "arguments": "case"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Set up jail module", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "money", "description": "Money commands", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "N/A"}, {"name": "money set", "description": "Set the money of a given economy user to a specific amount", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "type, user, amount"}, {"name": "money add", "description": "Add money to a given economy user", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "type, user, amount"}, {"name": "money remove", "description": "Remove money from a given economy user", "permissions": "bot manager", "aliases": [], "category": "moderator", "arguments": "type, user, amount"}, {"name": "jailed", "description": "Returns the jailed members", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "eightball", "description": "Ask the 8ball a question", "permissions": "N/A", "aliases": ["8ball"], "category": "fun", "arguments": "question"}, {"name": "buttonrole", "description": "Buttonrole commands", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "buttonrole unique", "description": "Set whether the roles on a message are unique (only one role allowed).", "permissions": "Set button role unique behavior", "aliases": [], "category": "autorole", "arguments": "message, unique"}, {"name": "buttonrole list", "description": "View a list of every button role", "permissions": "N/A", "aliases": [], "category": "autorole", "arguments": "N/A"}, {"name": "buttonrole remove", "description": "Remove a specific button from a message by its custom_id", "permissions": "manage guild", "aliases": [], "category": "autorole", "arguments": "button_id"}, {"name": "buttonrole clear", "description": "Remove all buttons from a message", "permissions": "manage roles", "aliases": [], "category": "autorole", "arguments": "message"}, {"name": "buttonrole add", "description": "Add a button to a message\n> If you don't want to use an emoji/label, just type `none`", "permissions": "manage roles", "aliases": [], "category": "autorole", "arguments": "message, role, label, emoji, color"}, {"name": "unjail", "description": "Lift the jail restriction from a member", "permissions": "manage messages", "aliases": [], "category": "moderation", "arguments": "member, reason"}, {"name": "quran", "description": "Get a random quran verse", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}, {"name": "history", "description": "History of moderation actions", "permissions": "N/A", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "history clear", "description": "Clear punishment history", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "history clear user", "description": "Clear all punishment history for a user", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "user"}, {"name": "history clear moderator", "description": "Clear all punishment history for a moderator", "permissions": "manage guild", "aliases": ["mod", "m"], "category": "moderation", "arguments": "user"}, {"name": "history clear all", "description": "Clear all punishment history for everyone", "permissions": "administrator", "aliases": [], "category": "moderation", "arguments": "N/A"}, {"name": "history remove", "description": "Remove a specific punishment entry by Guild ID", "permissions": "manage guild", "aliases": [], "category": "moderation", "arguments": "id"}, {"name": "history moderator", "description": "View a list of every punishment recorded by a given moderator", "permissions": "moderate members", "aliases": ["mod", "m"], "category": "moderation", "arguments": "user"}, {"name": "history user", "description": "View a list of every punishment recorded of a given user", "permissions": "manage messages", "aliases": ["u"], "category": "moderation", "arguments": "user"}, {"name": "bible", "description": "Get a random bible verse", "permissions": "N/A", "aliases": [], "category": "fun", "arguments": "N/A"}]