#!/usr/bin/env python3
"""
Simple R2 Connection Test
"""

import asyncio
import sys
import os

# Add the evelina directory to the path
sys.path.append('evelina')

from modules.handlers.s3 import S3<PERSON>and<PERSON>
from modules.config import CLOUDFLARE

async def simple_test():
    """Simple R2 connection test"""
    print("🔧 Simple R2 Connection Test")
    print("=" * 40)
    print(f"📋 Endpoint: {CLOUDFLARE.R2_ENDPOINT_URL}")
    print(f"🔑 Access Key: {CLOUDFLARE.R2_ACCESS_KEY_ID}")
    print(f"🔐 Secret Key: {CLOUDFLARE.R2_SECRET_ACCESS_KEY[:10]}...")
    
    # Initialize S3 handler
    r2 = S3Handler()
    
    try:
        # Test upload to stun bucket
        print("\n📤 Testing upload to 'stun' bucket...")
        test_data = b"Hello from R2 test!"
        test_filename = "test.txt"
        
        upload_result = await r2.upload_file("stun", test_data, test_filename, "text/plain")
        print(f"Upload result: {upload_result}")
        
        if upload_result.get('success'):
            print("✅ Upload successful!")
            
            # Test file exists
            print("\n🔍 Testing file exists...")
            exists = await r2.file_exists("stun", test_filename)
            print(f"File exists: {exists}")
            
            # Test public URL
            print("\n🌐 Testing public URL...")
            public_url = f"https://cdn.stun.lat/{test_filename}"
            print(f"Public URL: {public_url}")
            print("Try accessing this URL in your browser")
            
            # Clean up
            print("\n🗑️ Cleaning up...")
            delete_result = await r2.delete_file("stun", test_filename)
            print(f"Delete result: {delete_result}")
            
        else:
            print(f"❌ Upload failed: {upload_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test())
