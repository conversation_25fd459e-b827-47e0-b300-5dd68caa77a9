# Nginx configuration for HTTPS API
# Place this file in /etc/nginx/sites-available/api.stun.lat
# Then create a symlink: sudo ln -s /etc/nginx/sites-available/api.stun.lat /etc/nginx/sites-enabled/

# Rate limiting zone (must be in http block, but we'll put it in main nginx.conf)
# Add this line to /etc/nginx/nginx.conf in the http block:
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

server {
    listen 80;
    server_name api.stun.lat;

    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.stun.lat;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.stun.lat/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.stun.lat/privkey.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Rate limiting (using zone defined in main nginx.conf)
    limit_req zone=api burst=20 nodelay;

    # Static files (if needed) - proxy to static server on port 8081
    location ~ ^/[a-f0-9]+/?$ {
        proxy_pass http://127.0.0.1:8081;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_valid 200 1h;
        add_header Cache-Control "public, max-age=3600";
    }

    # API endpoints - proxy to your Python app on port 8080
    location / {
        # Proxy settings
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
    }

    # Logging
    access_log /var/log/nginx/api.stun.lat.access.log;
    error_log /var/log/nginx/api.stun.lat.error.log;
}
