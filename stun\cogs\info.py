import os
import random
import asyncio

from modules.styles import emojis, colors
from modules.evelinabot import <PERSON><PERSON>
from modules.helpers import EvelinaContext
from modules.validators import ValidTime

import discord
from discord import User, Embed, __version__, utils, Permissions, ClientUser, Status
from discord.ext.commands import Cog, command, hybrid_command, cooldown, BucketType
from discord.ui import View, Button

from datetime import datetime
from platform import python_version

class Info(Cog):
    def __init__(self, bot: <PERSON><PERSON>):
        self.bot = bot
        self.description = "Information commands"

    def create_bot_invite(self, user: User) -> View:
        """Create a view containing a button with the bot invite url"""
        view = View()
        view.add_item(Button(label=f"Invite {user.name}", url=utils.oauth_url(client_id=user.id, permissions=Permissions(8))))
        return view
    
    @hybrid_command(name="commands", aliases=["h", "cmds"], usage="help userinfo", description="View all commands or get help for a specific command")
    async def _help(self, ctx: <PERSON>linaContext, *, command: str = None):
        """View all commands or get help for a specific command"""
        if not command:
            return await ctx.send_help()
        _command = self.bot.get_command(command)
        if _command is None:
            return await ctx.send_warning(f'No command called `{command}` found')
        cog = _command.cog_name
        if cog and isinstance(cog, str) and cog.lower() in ["jishaku", "owner", "auth", "helper"]:
            if ctx.author.id not in self.bot.owner_ids:
                staff = await self.bot.db.fetchrow("SELECT * FROM team_members WHERE user_id = $1", ctx.author.id)
                if not staff:
                    return await ctx.send_warning(f"No command called `{command}` found")
        if _command.hidden:
            return await ctx.send_warning(f'No command called `{command}` found')
        return await ctx.send_help(_command)

    @hybrid_command(name="ping", cooldown=5)
    @cooldown(1, 5, BucketType.user)
    async def ping(self, ctx: EvelinaContext):
        """Displays the bot's latency"""
        start_time = datetime.utcnow()
        
        # Cool ping messages
        ping_phrases = [
            "🚀 Zooming through cyberspace at",
            "⚡ Lightning-fast response time:",
            "🌟 Stars aligned at speed of",
            "🎮 Game-ready latency at",
            "🌈 Rainbow connection speed:",
            "🔥 Hot connection coming in at",
            "🎯 Precision ping clocking",
            "💫 Cosmic connection speed:",
            "⚔️ Battle-ready latency of",
            "🎭 Performance check shows"
        ]
        
        # Get initial latencies
        websocket_latency = round(self.bot.latency * 1000)
        
        # Create loading message with dynamic ASCII animation
        loading_msg = await ctx.reply("```\n◌ Measuring cosmic latency...```")
        
        # Simulate processing with ASCII animation
        animations = ["◌", "◑", "◉", "◑"]
        for i in range(2):  # Do 2 cycles of animation
            for frame in animations:
                await loading_msg.edit(content=f"```\n{frame} Calculating quantum response time...```")
                await asyncio.sleep(0.2)
        
        # Calculate final latencies
        end_time = datetime.utcnow()
        api_latency = round((end_time - start_time).total_seconds() * 1000)
        
        # Create embedded response
        embed = Embed(
            title="🎯 Latency Check",
            color=0x2B2D31 if websocket_latency < 200 else 0xFAA61A if websocket_latency < 500 else 0xF04747
        )
        
        phrase = random.choice(ping_phrases)
        embed.description = f"{phrase} `{websocket_latency}ms`\n\n"
        embed.description += f"```ml\nWebSocket: {websocket_latency}ms\nAPI: {api_latency}ms\nRound Trip: {api_latency + websocket_latency}ms```"
        
        # Add a fun status indicator
        if websocket_latency < 100:
            embed.set_footer(text="🚀 Hyperspeed Connection | Ready for Action!")
        elif websocket_latency < 200:
            embed.set_footer(text="⚡ Excellent Connection | Full Speed Ahead!")
        elif websocket_latency < 500:
            embed.set_footer(text="✨ Good Connection | We're Cruising!")
        else:
            embed.set_footer(text="🐢 Slow but Steady | We'll Get There!")
            
        await loading_msg.edit(content="", embed=embed)
    
    def analyze_code(self, directory: str):
        total_files = 0
        total_lines = 0
        total_classes = 0
        total_functions = 0
        total_imports = 0
        directories = ['cogs', 'modules', 'events']
        for directory in directories:
            for root, _, files in os.walk(directory):
                for file in files:
                    if file.endswith(".py"):
                        total_files += 1
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            total_lines += len(f.read().splitlines())
                        classes, functions, imports = self.count_elements_in_file(file_path)
                        total_classes += classes
                        total_functions += functions
                        total_imports += imports
        return total_files, total_lines, total_classes, total_functions, total_imports

    def count_elements_in_file(self, file_path: str):
        classes = 0
        functions = 0
        imports = 0
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    stripped = line.strip()
                    if stripped.startswith("class "):
                        classes += 1
                    elif stripped.startswith("def ") or stripped.startswith("async def "):
                        functions += 1
                    elif stripped.startswith("import ") or stripped.startswith("from "):
                        imports += 1
        except Exception:
            pass
        return classes, functions, imports


    @hybrid_command(name="botinfo", aliases=["bi", "bot", "about"])
    async def botinfo(self, ctx: EvelinaContext):
        """Displays information about the bot"""
        data = await self.bot.session.get_json("https://api.stun.lat/health") or {}

        # Calculate statistics
        text_channels = sum(1 for g in self.bot.guilds for c in g.text_channels)
        voice_channels = sum(1 for g in self.bot.guilds for c in g.voice_channels)
        categorie_channels = sum(1 for g in self.bot.guilds for c in g.categories)
        total_channels = text_channels + voice_channels + categorie_channels
        guilds_count = sum(
            cluster.get("guilds", 0)
            for cluster in data.get("clusters", {}).values()
        )
        
        # Calculate code statistics
        files, lines, classes, functions, imports = self.analyze_code("stun")
        
        # Create dynamic status indicator
        status_emoji = {
            Status.online: "🟢",
            Status.idle: "🟡",
            Status.dnd: "🔴",
            Status.offline: "⚫"
        }.get(self.bot.status, "🟣")
        
        # Create main embed
        embed = Embed(color=0x2B2D31)
        
        # Set author with status
        embed.set_author(
            name=f"{self.bot.user.name} Status Dashboard {status_emoji}",
            icon_url=self.bot.user.avatar.url if self.bot.user.avatar else None
        )
        
        # Create dynamic description
        embed.description = (
            f"```ml\n💠 Premium Multi-Purpose Discord Bot\n"
            f"⚡ Powered by {self.bot.shard_count} Shards\n"
            f"🌟 Created by Stun Team```\n"
            f"Making Discord better for **{data.get('users', 0):,}** members across "
            f"**{guilds_count:,}** servers!\n\n"
            f"[Support Server](https://discord.gg/stun) • [Website](https://stun.lat) • "
            f"[Vote for Us](https://top.gg/bot/{self.bot.user.id})"
        )

        # Statistics Section
        embed.add_field(
            name="📊 Statistics",
            value=(
                f"```yml\n"
                f"Members: {data.get('users', 0):,}\n"
                f"Servers: {guilds_count:,}\n"
                f"Channels: {total_channels:,}\n"
                f"Commands: {len(set(self.bot.walk_commands()))}\n"
                f"```"
            ),
            inline=True
        )

        # Technical Info
        embed.add_field(
            name="⚙️ Technical",
            value=(
                f"```yml\n"
                f"Python: {python_version()}\n"
                f"Discord.py: {__version__}\n"
                f"Files: {files}\n"
                f"Code Lines: {lines:,}\n"
                f"```"
            ),
            inline=True
        )

        # Channel Distribution
        embed.add_field(
            name="🌐 Channels",
            value=(
                f"```ml\n"
                f"Text: {text_channels:,}\n"
                f"Voice: {voice_channels:,}\n"
                f"Categories: {categorie_channels:,}\n"
                f"```"
            ),
            inline=True
        )

        # Add uptime information
        embed.add_field(
            name="⏰ Uptime",
            value=f"```\n{self.bot.uptime}```",
            inline=False
        )

        # Create buttons view
        view = View()
        view.add_item(Button(
            style=discord.ButtonStyle.url,
            label="✨ Invite Me",
            url=f"https://discord.com/oauth2/authorize?client_id={self.bot.user.id}&permissions=8&scope=bot%20applications.commands"
        ))
        view.add_item(Button(
            style=discord.ButtonStyle.url,
            label="🌐 Website",
            url="https://stun.lat"
        ))
        view.add_item(Button(
            style=discord.ButtonStyle.url,
            label="💝 Support",
            url="https://discord.gg/stun"
        ))

        # Set footer with fun fact
        fun_facts = [
            "We're constantly improving our bot!",
            "We're always looking for new features!",
            "We're always improving our bot!",
            "We're always looking for new features!",
            "We're always improving our bot!",
            "Our code is constantly evolving with new features!",
            "We're powered by Python's asyncio for lightning-fast responses!",
            "Every command is optimized for maximum performance!"
        ]
        embed.set_footer(text=f"🎯 {random.choice(fun_facts)}")
        
        return await ctx.send(embed=embed, view=view)
    
    @hybrid_command(name="shards")
    async def shards(self, ctx: EvelinaContext):
        """Check status of each bot shard"""
        
        data = await self.bot.session.get_json("https://api.stun.lat/health") or {}
        
        if not data:
            await ctx.send("Could not fetch shard data from API")
            return
        
        total_users = data.get("users", 0)
        total_guilds = data.get("guilds", 0)
        clusters = data.get("clusters", {})
        current_guild_shard_id = ctx.guild.shard_id
        
        shards_data = []
        for cluster_id, cluster_info in clusters.items():
            for shard in cluster_info.get("shards", []):
                shard_info = {
                    "shard_id": shard["id"],
                    "ping": round(shard["latency"] * 1000),
                    "guilds": shard["guilds"],
                    "users": shard["users"],
                    "is_current": shard["id"] == current_guild_shard_id,
                    "uptime": shard["uptime"],
                    "seconds_since_seen": shard["seconds_since_seen"]
                }
                shards_data.append(shard_info)
        
        shards_data.sort(key=lambda x: x["shard_id"])
        
        entries_per_page = 6
        total_pages = (len(shards_data) + entries_per_page - 1) // entries_per_page
        embeds = []
        
        for i in range(0, len(shards_data), entries_per_page):
            page_number = (i // entries_per_page) + 1
            embed = Embed(color=colors.NEUTRAL, title=f"Total shards ({len(shards_data)})")
            embed.set_image(url="https://storagevault.cloud/users/bender/output-onlinepngtools_(2).png")
            
            embed.description = f"**Total Users**: {total_users:,}\n**Total Guilds**: {total_guilds:,}"
            
            for shard_info in shards_data[i:i + entries_per_page]:
                shard_id = shard_info["shard_id"]
                shard_field_name = f"Shard {shard_id}"
                
                if shard_info["is_current"]:
                    shard_field_name += f" {emojis.LEFT} You"
                    
                shard_field_value = (
                    f"**ping**: {shard_info['ping']}ms\n"
                    f"**guilds**: {shard_info['guilds']}\n"
                    f"**users**: {shard_info['users']:,}"
                )
                
                embed.add_field(name=shard_field_name, value=shard_field_value, inline=True)
                
            embeds.append(embed)
        
        await ctx.paginator(embeds)

    @hybrid_command(name="invite", aliases=["link"])
    async def invite(self, ctx: EvelinaContext):
        """Send an invite link of the bot"""
        await ctx.reply(view=self.create_bot_invite(ctx.guild.me))

    @command(name="uptime", aliases=["up"])
    async def uptime(self, ctx: EvelinaContext):
        """Displays how long has the bot been online for"""
        return await ctx.reply(embed=Embed(color=colors.NEUTRAL, description=f"🕐 {ctx.author.mention}: **{self.bot.uptime}**"))
    
    @command(name="getbotinvite", aliases=["gbi"], usage="getbotinvite stun#3424")
    async def getbotinvite(self, ctx: EvelinaContext, *, member: User):
        """Get the bot invite based on it's id"""
        if not member.bot:
            return await ctx.send_warning("This is **not** a bot")
        await ctx.reply(view=self.create_bot_invite(member))

    @command(name="topcommands", aliases=["topcmds"])
    async def topcommands(self, ctx: EvelinaContext, time: ValidTime = None):
        """View the top 50 most used commands"""
        base_query = "SELECT command, COUNT(command) AS usage_count FROM command_history"
        if time:
            since = datetime.now().timestamp() - time
            since_time = datetime.fromtimestamp(since)
            query = f"{base_query} WHERE timestamp >= EXTRACT(EPOCH FROM TIMESTAMP '{since_time}') GROUP BY command ORDER BY usage_count DESC LIMIT 50"
            results = await self.bot.db.fetch(query)
        else:
            query = f"{base_query} GROUP BY command ORDER BY usage_count DESC LIMIT 50"
            results = await self.bot.db.fetch(query)
        if not results:
            return await ctx.send_warning("No usage found")
        to_show = [f"**{check['command']}** used `{check['usage_count']:,.0f}` times" for check in results]
        since_message = f"since {since_time.strftime('%m/%d/%Y %H:%M')}" if time else "overall"
        return await ctx.paginate(to_show, f"Top commands {since_message}", {"name": ctx.author.name, "icon_url": ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url})

async def setup(bot: Evelina) -> None:
    return await bot.add_cog(Info(bot))