#!/bin/bash

# Complete SSL Setup Script for api.stun.lat
echo "🔒 Complete SSL setup for api.stun.lat..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

echo "📋 Step 1: Setting up temporary HTTP configuration..."

# Copy temporary configuration (without SSL)
cp nginx-api-config-temp.conf /etc/nginx/sites-available/api.stun.lat

# Create symlink to enable the site
ln -sf /etc/nginx/sites-available/api.stun.lat /etc/nginx/sites-enabled/

# Create web root for Let's Encrypt challenges
mkdir -p /var/www/html

# Test temporary configuration
echo "🧪 Testing temporary Nginx configuration..."
if nginx -t; then
    echo "✅ Temporary configuration is valid"
    systemctl reload nginx
    echo "🔄 Nginx reloaded with temporary config"
else
    echo "❌ Temporary configuration has errors"
    exit 1
fi

echo ""
echo "📋 Step 2: Obtaining SSL certificate..."
echo "⚠️  Make sure api.stun.lat points to this server's IP address!"
echo "⚠️  Your bot should be running on port 8080 for this to work!"
echo ""

# Check if domain resolves to this server
echo "🔍 Checking DNS resolution..."
DOMAIN_IP=$(dig +short api.stun.lat)
SERVER_IP=$(curl -s ifconfig.me)

if [ "$DOMAIN_IP" = "$SERVER_IP" ]; then
    echo "✅ DNS is correctly configured"
else
    echo "⚠️  Warning: api.stun.lat ($DOMAIN_IP) doesn't point to this server ($SERVER_IP)"
    echo "   This might cause SSL certificate generation to fail."
fi

read -p "Press Enter to continue with SSL certificate generation..."

# Obtain SSL certificate
if certbot --nginx -d api.stun.lat --non-interactive --agree-tos --email <EMAIL>; then
    echo "✅ SSL certificate obtained successfully!"
else
    echo "❌ Failed to obtain SSL certificate"
    echo "   Please check:"
    echo "   1. DNS: api.stun.lat points to this server"
    echo "   2. Firewall: Port 80 is open"
    echo "   3. Bot: Running on port 8080"
    exit 1
fi

echo ""
echo "📋 Step 3: Installing final HTTPS configuration..."

# Now install the full HTTPS configuration
cp nginx-api-config.conf /etc/nginx/sites-available/api.stun.lat

# Test final configuration
echo "🧪 Testing final HTTPS configuration..."
if nginx -t; then
    echo "✅ Final configuration is valid"
    systemctl reload nginx
    echo "🔄 Nginx reloaded with HTTPS config"
else
    echo "❌ Final configuration has errors"
    echo "   Reverting to temporary config..."
    cp nginx-api-config-temp.conf /etc/nginx/sites-available/api.stun.lat
    systemctl reload nginx
    exit 1
fi

# Enable and start Nginx
systemctl enable nginx
systemctl start nginx

# Set up automatic certificate renewal
echo "🔄 Setting up automatic certificate renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo ""
echo "🎉 HTTPS setup complete!"
echo ""
echo "📋 Summary:"
echo "   • SSL certificate installed for api.stun.lat"
echo "   • Nginx configured with HTTPS and security headers"
echo "   • HTTP traffic automatically redirected to HTTPS"
echo "   • Rate limiting configured (10 req/s with burst of 20)"
echo "   • Certificate auto-renewal configured"
echo ""
echo "🧪 Testing endpoints:"
echo "   curl https://api.stun.lat/health"
echo "   curl https://api.stun.lat/commands"
echo ""
echo "🔧 Next steps:"
echo "   1. Restart your bot to use port 8080"
echo "   2. Test bot commands: /botinfo and /shards"
echo "   3. Verify all API endpoints work with HTTPS"
echo ""
echo "📊 Nginx status:"
systemctl status nginx --no-pager -l
