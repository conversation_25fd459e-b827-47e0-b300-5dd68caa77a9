from typing import Optional, Dict, Any
import discord
from discord import ui
import datetime
from io import BytesIO
import asyncio
import aiohttp

class TikTokBaseView(discord.ui.View):
    def __init__(self, bot, ctx, timeout: int = 180):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.ctx = ctx
        self.message: Optional[discord.Message] = None

    async def on_timeout(self) -> None:
        """Disable all buttons when the view times out"""
        for item in self.children:
            item.disabled = True
        if self.message:
            await self.message.edit(view=self)

class TikTokVideoView(TikTokBaseView):
    def __init__(self, bot, ctx, video_data: Dict[str, Any], video_bytes: bytes):
        super().__init__(bot, ctx)
        self.video_data = video_data
        self.video_bytes = video_bytes
        self._audio_extracted = False

    @discord.ui.button(label="Stats", style=discord.ButtonStyle.primary, emoji="📊")
    async def show_stats(self, interaction: discord.Interaction, button: discord.ui.Button):
        author_info = self.video_data["author"]
        stats = self.video_data["stats"]
        
        embed = discord.Embed(
            title="Video Statistics",
            color=0x000000,
            timestamp=datetime.datetime.fromtimestamp(int(self.video_data["createTime"]))
        )
        embed.set_author(
            name=f"{author_info['nickname']} (@{author_info['uniqueId']})",
            icon_url=author_info["avatarLarger"]
        )

        stats_text = [
            f"👀 Views: {stats.get('playCount', 0):,}",
            f"❤️ Likes: {stats.get('diggCount', 0):,}",
            f"💬 Comments: {stats.get('commentCount', 0):,}",
            f"↪️ Shares: {stats.get('shareCount', 0):,}"
        ]
        embed.add_field(name="Engagement", value="\n".join(stats_text), inline=False)

        await interaction.response.edit_message(embed=embed)

    @discord.ui.button(label="Video Info", style=discord.ButtonStyle.primary, emoji="🎬")
    async def show_video_info(self, interaction: discord.Interaction, button: discord.ui.Button):
        video_details = self.video_data["video"]
        size_mb = len(self.video_bytes) / (1024 * 1024)
        bitrate = len(self.video_bytes) * 8 / (video_details.get("duration", 1) * 1000)  # kbps

        embed = discord.Embed(
            title="Video Technical Details",
            color=0x000000
        )
        video_specs = [
            "🎬 Format: MP4",
            f"📊 Resolution: {video_details.get('width', 0)}x{video_details.get('height', 0)}",
            f"📦 Size: {size_mb:.2f}MB",
            f"⚡ Bitrate: {bitrate:.1f}kbps",
            f"⏱️ Duration: {video_details.get('duration', 0)}s"
        ]
        embed.add_field(name="Specifications", value="\n".join(video_specs), inline=False)

        flags = []
        if self.video_data.get("duetEnabled"):
            flags.append("🎵 Duets Enabled")
        if self.video_data.get("stitchEnabled"):
            flags.append("🔄 Stitches Enabled")
        if self.video_data.get("privateItem"):
            flags.append("🔒 Private")
        if music_info := self.video_data.get("music"):
            flags.append(f"🎼 Sound: {music_info.get('title', 'Original Sound')}")
        
        if flags:
            embed.add_field(name="Features", value="\n".join(flags), inline=False)

        await interaction.response.edit_message(embed=embed)

    @discord.ui.button(label="Extract Audio", style=discord.ButtonStyle.primary, emoji="🎵")
    async def extract_audio(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self._audio_extracted:
            await interaction.response.send_message("Audio has already been extracted!", ephemeral=True)
            return

        await interaction.response.defer(thinking=True)

        try:
            # Get music info
            music_info = self.video_data.get("music", {})
            title = music_info.get("title", "Original Sound")
            author = music_info.get("authorName", self.video_data["author"]["uniqueId"])
            
            # Use TikTok's music URL if available
            if music_url := music_info.get("playUrl"):
                async with aiohttp.ClientSession() as session:
                    async with session.get(music_url) as response:
                        if response.status == 200:
                            audio_data = await response.read()
                            
                            # Send audio file
                            file = discord.File(
                                BytesIO(audio_data), 
                                filename=f"{author} - {title}.mp3"
                            )
                            
                            embed = discord.Embed(
                                title="Audio Extracted",
                                description=f"**Title**: {title}\n**Author**: {author}",
                                color=0x000000
                            )
                            
                            await interaction.followup.send(embed=embed, file=file)
                            self._audio_extracted = True
                            return
            
            # If no music URL available, try to use the video's audio
            if audio_url := self.video_data.get("video", {}).get("musicUrl"):
                async with aiohttp.ClientSession() as session:
                    async with session.get(audio_url) as response:
                        if response.status == 200:
                            audio_data = await response.read()
                            
                            # Send audio file
                            file = discord.File(
                                BytesIO(audio_data), 
                                filename=f"{author} - {title}.mp3"
                            )
                            
                            embed = discord.Embed(
                                title="Audio Extracted",
                                description=f"**Title**: {title}\n**Author**: {author}",
                                color=0x000000
                            )
                            
                            await interaction.followup.send(embed=embed, file=file)
                            self._audio_extracted = True
                            return
            
            # If no direct audio URL is available, inform the user
            await interaction.followup.send("Sorry, audio extraction is not available for this video.", ephemeral=True)
            
        except Exception as e:
            await interaction.followup.send(f"Failed to extract audio: {str(e)}", ephemeral=True)

class TikTokProfileView(TikTokBaseView):
    def __init__(self, bot, ctx, user_data: Dict[str, Any]):
        super().__init__(bot, ctx)
        self.user_data = user_data

    @discord.ui.button(label="Profile", style=discord.ButtonStyle.primary, emoji="👤")
    async def show_profile(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title=f"{self.user_data['nickname']} (@{self.user_data['uniqueId']})",
            color=0x000000,
            timestamp=datetime.datetime.now()
        )
        embed.set_thumbnail(url=self.user_data["avatarLarger"])

        if self.user_data.get("verified"):
            embed.title += " ☑️"

        stats = [
            f"👥 Followers: {self.user_data['stats']['followerCount']:,}",
            f"👤 Following: {self.user_data['stats']['followingCount']:,}",
            f"❤️ Likes: {self.user_data['stats']['heartCount']:,}",
            f"📹 Videos: {self.user_data['stats'].get('videoCount', 0):,}"
        ]
        embed.add_field(name="Stats", value="\n".join(stats), inline=False)

        # Add bio if available
        if bio := self.user_data.get("signature"):
            embed.add_field(name="Bio", value=bio, inline=False)

        await interaction.response.edit_message(embed=embed)

    @discord.ui.button(label="Links", style=discord.ButtonStyle.primary, emoji="🔗")
    async def show_links(self, interaction: discord.Interaction, button: discord.ui.Button):
        embed = discord.Embed(
            title="Social Links",
            color=0x000000
        )

        links = []
        if instagram := self.user_data.get("instagramId"):
            links.append(f"[Instagram](https://instagram.com/{instagram})")
        if youtube := self.user_data.get("youtubeChannelId"):
            links.append(f"[YouTube](https://youtube.com/channel/{youtube})")
        if twitter := self.user_data.get("twitterId"):
            links.append(f"[Twitter](https://twitter.com/{twitter})")
        
        if not links:
            links = ["No social links available"]

        embed.description = "\n".join(links)
        await interaction.response.edit_message(embed=embed)
