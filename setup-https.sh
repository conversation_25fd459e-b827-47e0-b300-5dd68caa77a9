#!/bin/bash

# HTTPS Setup Script for api.stun.lat
# This script sets up Nginx with SSL certificates for your API

echo "🔒 Setting up HTTPS for api.stun.lat..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Update system packages
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Nginx if not already installed
if ! command -v nginx &> /dev/null; then
    echo "📥 Installing Nginx..."
    apt install nginx -y
else
    echo "✅ Nginx is already installed"
fi

# Install Certbot for Let's Encrypt SSL certificates
if ! command -v certbot &> /dev/null; then
    echo "📥 Installing Certbot..."
    apt install certbot python3-certbot-nginx -y
else
    echo "✅ Certbot is already installed"
fi

# Copy Nginx configuration
echo "⚙️ Setting up Nginx configuration..."
cp nginx-api-config.conf /etc/nginx/sites-available/api.stun.lat

# Add rate limiting to main nginx.conf if not already present
if ! grep -q "limit_req_zone.*zone=api" /etc/nginx/nginx.conf; then
    echo "📝 Adding rate limiting configuration to nginx.conf..."
    sed -i '/http {/a\\tlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;' /etc/nginx/nginx.conf
fi

# Create symlink to enable the site
ln -sf /etc/nginx/sites-available/api.stun.lat /etc/nginx/sites-enabled/

# Remove default Nginx site if it exists
if [ -f /etc/nginx/sites-enabled/default ]; then
    rm /etc/nginx/sites-enabled/default
    echo "🗑️ Removed default Nginx site"
fi

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
if nginx -t; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration has errors. Please check the config file."
    exit 1
fi

# Reload Nginx
systemctl reload nginx
echo "🔄 Nginx reloaded"

# Obtain SSL certificate
echo "🔐 Obtaining SSL certificate for api.stun.lat..."
echo "⚠️  Make sure api.stun.lat points to this server's IP address!"
read -p "Press Enter to continue when DNS is configured..."

certbot --nginx -d api.stun.lat --non-interactive --agree-tos --email <EMAIL>

# Enable and start Nginx
systemctl enable nginx
systemctl start nginx

# Set up automatic certificate renewal
echo "🔄 Setting up automatic certificate renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo ""
echo "🎉 HTTPS setup complete!"
echo ""
echo "📋 Summary:"
echo "   • Nginx is configured to proxy requests to your Python app on port 8080"
echo "   • SSL certificate is installed for api.stun.lat"
echo "   • HTTP traffic is automatically redirected to HTTPS"
echo "   • Certificate auto-renewal is configured"
echo ""
echo "🔧 Next steps:"
echo "   1. Restart your bot to use port 8080"
echo "   2. Test your API: https://api.stun.lat/health"
echo "   3. Update any hardcoded HTTP URLs to HTTPS"
echo ""
echo "📊 Nginx status:"
systemctl status nginx --no-pager -l
