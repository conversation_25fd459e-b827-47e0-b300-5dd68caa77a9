#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create essential database tables for the bot.
This is a fallback if the full SQL dump doesn't work.
"""

import asyncio
import asyncpg
import os
from modules import config

async def create_essential_tables():
    """Create the most essential tables needed for the bot to function"""
    
    # Connect to database
    conn = await asyncpg.connect(
        host=config.POSTGRES.HOST,
        port=config.POSTGRES.PORT,
        user=config.POSTGRES.USER,
        password=config.POSTGRES.PASSWORD,
        database=config.POSTGRES.DATABASE
    )
    
    try:
        print("Creating essential tables...")
        
        # Essential tables for basic bot functionality
        essential_tables = [
            # Prefixes table
            """
            CREATE TABLE IF NOT EXISTS prefixes (
                guild_id BIGINT PRIMARY KEY,
                prefix TEXT NOT NULL
            )
            """,
            
            # Self prefix table
            """
            CREATE TABLE IF NOT EXISTS selfprefix (
                user_id BIGINT PRIMARY KEY,
                prefix TEXT NOT NULL
            )
            """,
            
            # Blacklist table
            """
            CREATE TABLE IF NOT EXISTS blacklist_user (
                user_id BIGINT PRIMARY KEY,
                moderator_id BIGINT NOT NULL,
                duration BIGINT,
                reason TEXT NOT NULL,
                timestamp BIGINT NOT NULL
            )
            """,
            
            # Command stats table
            """
            CREATE TABLE IF NOT EXISTS command_stats (
                id SERIAL PRIMARY KEY,
                command VARCHAR(255) NOT NULL,
                user_id BIGINT NOT NULL,
                guild_id BIGINT,
                channel_id BIGINT NOT NULL,
                execution_time DOUBLE PRECISION NOT NULL,
                timestamp TIMESTAMP WITHOUT TIME ZONE NOT NULL,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # Command history table
            """
            CREATE TABLE IF NOT EXISTS command_history (
                id SERIAL PRIMARY KEY,
                command TEXT,
                arguments TEXT,
                server_id BIGINT,
                user_id BIGINT,
                channel_id BIGINT,
                timestamp BIGINT
            )
            """,
            
            # Error codes table
            """
            CREATE TABLE IF NOT EXISTS error_codes (
                code VARCHAR(30) PRIMARY KEY,
                info JSON
            )
            """,
            
            # Economy table
            """
            CREATE TABLE IF NOT EXISTS economy (
                user_id BIGINT PRIMARY KEY,
                cash DOUBLE PRECISION DEFAULT 0,
                card DOUBLE PRECISION DEFAULT 0,
                daily BIGINT DEFAULT 0,
                daily_streak BIGINT DEFAULT 0,
                rob BIGINT DEFAULT 0,
                work BIGINT DEFAULT 0,
                bonus BIGINT DEFAULT 0,
                item_bank BIGINT DEFAULT 50000,
                slut BIGINT DEFAULT 0,
                item_booster BIGINT DEFAULT 0,
                item_booster_until BIGINT DEFAULT 0,
                quest BIGINT DEFAULT 0,
                item_case BIGINT DEFAULT 0,
                beg BIGINT DEFAULT 0,
                terms BOOLEAN DEFAULT false,
                item_case_blackice BIGINT DEFAULT 0
            )
            """,
            
            # AFK table
            """
            CREATE TABLE IF NOT EXISTS afk (
                user_id BIGINT,
                reason TEXT,
                time TIMESTAMP WITH TIME ZONE
            )
            """,
            
            # Aliases table
            """
            CREATE TABLE IF NOT EXISTS aliases (
                guild_id BIGINT NOT NULL,
                command TEXT NOT NULL,
                alias TEXT NOT NULL,
                args TEXT
            )
            """,
            
            # Self aliases table
            """
            CREATE TABLE IF NOT EXISTS selfaliases (
                user_id BIGINT NOT NULL,
                command TEXT NOT NULL,
                alias TEXT NOT NULL,
                args TEXT
            )
            """,
            
            # Activity ignore table
            """
            CREATE TABLE IF NOT EXISTS activity_ignore (
                guild_id BIGINT,
                channel_id BIGINT
            )
            """,
            
            # Guild disabled commands
            """
            CREATE TABLE IF NOT EXISTS guild_disabled_commands (
                guild_id BIGINT,
                cmd TEXT
            )
            """,
            
            # Guild disabled modules
            """
            CREATE TABLE IF NOT EXISTS guild_disabled_module (
                guild_id BIGINT NOT NULL,
                module TEXT NOT NULL
            )
            """,
            
            # Voicemaster buttons (for views)
            """
            CREATE TABLE IF NOT EXISTS voicemaster_buttons (
                guild_id BIGINT,
                channel_id BIGINT,
                message_id BIGINT
            )
            """
        ]
        
        created_count = 0
        for table_sql in essential_tables:
            try:
                await conn.execute(table_sql)
                created_count += 1
            except Exception as e:
                print(f"Failed to create table: {e}")
        
        print(f"Successfully created {created_count}/{len(essential_tables)} essential tables")
        
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_essential_tables())
