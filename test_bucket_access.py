import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'evelina'))

from modules.handlers.s3 import S3<PERSON><PERSON><PERSON>

async def test_bucket_access():
    print("🔧 Testing Bucket Access and Configuration")
    print("=" * 60)
    
    r2 = S3Handler()
    
    try:
        # Test 1: List bucket contents
        print("\n📋 Test 1: Listing bucket contents...")
        
        from aiobotocore.session import AioSession
        from modules import config
        
        session = AioSession()
        async with session.create_client(
            "s3",
            endpoint_url=config.CLOUDFLARE.R2_ENDPOINT_URL,
            aws_access_key_id=config.CLOUDFLARE.R2_ACCESS_KEY_ID,
            aws_secret_access_key=config.CLOUDFLARE.R2_SECRET_ACCESS_KEY,
        ) as client:
            
            # List objects in bucket
            response = await client.list_objects_v2(Bucket="stun")
            
            if 'Contents' in response:
                print(f"✅ Found {len(response['Contents'])} files in bucket:")
                for obj in response['Contents'][:10]:  # Show first 10 files
                    print(f"   📄 {obj['Key']} ({obj['Size']} bytes)")
                if len(response['Contents']) > 10:
                    print(f"   ... and {len(response['Contents']) - 10} more files")
            else:
                print("📭 Bucket is empty")
            
            # Test 2: Check bucket location and settings
            print("\n🌍 Test 2: Checking bucket location...")
            try:
                location = await client.get_bucket_location(Bucket="stun")
                print(f"✅ Bucket location: {location.get('LocationConstraint', 'us-east-1')}")
            except Exception as e:
                print(f"⚠️ Could not get bucket location: {e}")
            
            # Test 3: Check if we can read a specific file
            print("\n📖 Test 3: Testing file access...")
            try:
                # Try to read the public-test.txt file we uploaded
                response = await client.get_object(Bucket="stun", Key="public-test.txt")
                content = await response['Body'].read()
                print(f"✅ Successfully read file content: {content.decode()[:50]}...")
            except Exception as e:
                print(f"❌ Could not read file: {e}")
            
            # Test 4: Check bucket ACL
            print("\n🔐 Test 4: Checking bucket ACL...")
            try:
                acl = await client.get_bucket_acl(Bucket="stun")
                print(f"✅ Bucket owner: {acl['Owner']['DisplayName']}")
                print(f"📋 Grants: {len(acl.get('Grants', []))} permissions")
                for grant in acl.get('Grants', []):
                    grantee = grant.get('Grantee', {})
                    permission = grant.get('Permission')
                    if 'URI' in grantee:
                        print(f"   🌐 {grantee['URI']}: {permission}")
                    else:
                        print(f"   👤 {grantee.get('DisplayName', 'Unknown')}: {permission}")
            except Exception as e:
                print(f"⚠️ Could not get bucket ACL: {e}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_bucket_access())
