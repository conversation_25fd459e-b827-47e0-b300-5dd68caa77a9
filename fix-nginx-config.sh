#!/bin/bash

# Quick fix for the Nginx configuration error
echo "🔧 Fixing Nginx configuration..."

# Copy the corrected configuration
sudo cp nginx-api-config.conf /etc/nginx/sites-available/api.stun.lat

# Add rate limiting to main nginx.conf if not already present
if ! grep -q "limit_req_zone.*zone=api" /etc/nginx/nginx.conf; then
    echo "📝 Adding rate limiting configuration to nginx.conf..."
    sudo sed -i '/http {/a\\tlimit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;' /etc/nginx/nginx.conf
    echo "✅ Rate limiting configuration added"
else
    echo "✅ Rate limiting configuration already exists"
fi

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
if sudo nginx -t; then
    echo "✅ Nginx configuration is now valid!"
    
    # Reload Nginx
    echo "🔄 Reloading Nginx..."
    sudo systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
    
    echo ""
    echo "🎉 Configuration fixed! You can now continue with SSL setup:"
    echo "   sudo certbot --nginx -d api.stun.lat"
else
    echo "❌ Configuration still has errors. Please check manually."
    echo "   Check the error details above and fix accordingly."
fi
