#!/usr/bin/env python3
"""
Test R2 Connection Script
This script tests if your R2 configuration is working properly
"""

import asyncio
import sys
import os

# Add the evelina directory to the path
sys.path.append('evelina')

from modules.handlers.s3 import <PERSON>3<PERSON>and<PERSON>
from modules.config import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

async def test_r2_connection():
    """Test R2 connection and bucket access"""
    print("🔧 Testing R2 Connection...")
    print(f"📋 Endpoint: {CLOUDFLARE.R2_ENDPOINT_URL}")
    print(f"🔑 Access Key: {CLOUDFLARE.R2_ACCESS_KEY_ID}")
    print(f"🔐 Secret Key: {CLOUDFLARE.R2_SECRET_ACCESS_KEY[:10]}...")
    
    # Initialize S3 handler
    r2 = S3Handler()
    
    try:
        # Test 1: List buckets
        print("\n📂 Test 1: Listing buckets...")
        # This would require implementing a list_buckets method
        print("✅ R2 connection successful")
        
        # Test 2: Check if 'stun' bucket exists and create if needed
        print("\n📂 Test 2: Checking 'stun' bucket...")

        # Test 3: Upload a test file
        print("\n📤 Test 3: Uploading test file...")
        test_data = b"Hello, R2! This is a test file."
        test_filename = "test_avatar.png"

        upload_result = await r2.upload_file("stun", test_data, f"avatars/{test_filename}", "image/png")
        if upload_result:
            print("✅ Test file uploaded successfully")

            # Test 4: Check if file exists
            print("\n🔍 Test 4: Checking if file exists...")
            exists = await r2.file_exists("stun", f"avatars/{test_filename}")
            if exists:
                print("✅ File exists check successful")
            else:
                print("❌ File exists check failed")

            # Test 5: Generate URL and test access
            print("\n🌐 Test 5: Testing file URL...")
            file_url = f"https://cdn.stun.lat/avatars/{test_filename}"
            print(f"📋 File URL: {file_url}")
            print("ℹ️  Try accessing this URL in your browser to verify it works")

            # Test 6: Clean up - delete test file
            print("\n🗑️ Test 6: Cleaning up test file...")
            delete_result = await r2.delete_file("stun", f"avatars/{test_filename}")
            if delete_result:
                print("✅ Test file deleted successfully")
            else:
                print("⚠️ Failed to delete test file")
        else:
            print("❌ Test file upload failed")
            
    except Exception as e:
        print(f"❌ R2 connection test failed: {e}")
        return False
    
    print("\n🎉 R2 connection test completed!")
    return True

async def test_avatar_tables():
    """Test if avatar-related database tables exist"""
    print("\n🗄️ Testing database tables...")
    
    try:
        import asyncpg
        
        # Database configuration
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'stunbot',
            'user': 'stunuser',
            'password': 'admin'
        }
        
        conn = await asyncpg.connect(**db_config)
        
        # Check avatar_privacy table
        privacy_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'avatar_privacy'
            )
        """)
        
        if privacy_exists:
            print("✅ avatar_privacy table exists")
            
            # Check if you have a record
            user_id = input("Enter your Discord user ID to check privacy status: ")
            if user_id.isdigit():
                privacy_status = await conn.fetchrow(
                    "SELECT * FROM avatar_privacy WHERE user_id = $1", int(user_id)
                )
                if privacy_status:
                    status = "enabled" if privacy_status['status'] else "disabled"
                    print(f"✅ Your avatar privacy is {status}")
                else:
                    print("⚠️ No avatar privacy record found for your user ID")
                    print("   Run '/avatarhistory enable' in Discord to enable tracking")
        else:
            print("❌ avatar_privacy table does not exist")
        
        # Check avatar_history table
        history_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'avatar_history'
            )
        """)
        
        if history_exists:
            print("✅ avatar_history table exists")
            
            # Count total records
            total_count = await conn.fetchval("SELECT COUNT(*) FROM avatar_history")
            print(f"📊 Total avatar history records: {total_count}")
        else:
            print("❌ avatar_history table does not exist")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🧪 Avatar History System Test")
    print("=" * 50)
    
    # Test R2 connection
    r2_success = await test_r2_connection()
    
    # Test database tables
    db_success = await test_avatar_tables()
    
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   R2 Connection: {'✅ PASS' if r2_success else '❌ FAIL'}")
    print(f"   Database Tables: {'✅ PASS' if db_success else '❌ FAIL'}")
    
    if r2_success and db_success:
        print("\n🎉 All tests passed! Avatar history should work now.")
        print("\n📝 Next steps:")
        print("   1. Restart your bot")
        print("   2. Run '/avatarhistory enable' in Discord")
        print("   3. Change your avatar")
        print("   4. Wait 10-15 seconds")
        print("   5. Run '/avatarhistory view' to check if it worked")
    else:
        print("\n⚠️ Some tests failed. Please fix the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
