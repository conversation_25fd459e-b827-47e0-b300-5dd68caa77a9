import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'evelina'))

import asyncpg
from modules import config

async def debug_avatar_system():
    print("🔍 Debugging Avatar System")
    print("=" * 50)
    
    # Connect to database
    try:
        conn = await asyncpg.connect(
            host=config.POSTGRES.HOST,
            port=config.POSTGRES.PORT,
            user=config.POSTGRES.USER,
            password=config.POSTGRES.PASSWORD,
            database=config.POSTGRES.DATABASE
        )
        print("✅ Connected to database")
        
        # Check if avatar_privacy table exists
        print("\n📋 Checking avatar_privacy table...")
        tables = await conn.fetch("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'avatar_privacy'")
        if tables:
            print("✅ avatar_privacy table exists")
            
            # Check if you have avatar privacy enabled
            # Replace YOUR_USER_ID with your actual Discord user ID
            print("\n🔍 Checking your avatar privacy status...")
            print("Enter your Discord User ID:")
            user_id = input().strip()
            
            if user_id.isdigit():
                privacy_record = await conn.fetchrow("SELECT * FROM avatar_privacy WHERE user_id = $1", int(user_id))
                if privacy_record:
                    status = "ENABLED" if privacy_record['status'] else "DISABLED"
                    print(f"✅ Avatar privacy found: {status}")
                    if not privacy_record['status']:
                        print("❌ Avatar privacy is DISABLED - this is why it's not working!")
                        print("💡 Run '/avatarhistory enable' in Discord to fix this")
                else:
                    print("❌ No avatar privacy record found")
                    print("💡 Run '/avatarhistory enable' in Discord to create one")
            else:
                print("❌ Invalid user ID")
        else:
            print("❌ avatar_privacy table does not exist")
            
        # Check if avatar_history table exists
        print("\n📋 Checking avatar_history table...")
        tables = await conn.fetch("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'avatar_history'")
        if tables:
            print("✅ avatar_history table exists")
            
            # Check how many avatar records exist
            count = await conn.fetchval("SELECT COUNT(*) FROM avatar_history")
            print(f"📊 Total avatar records in database: {count}")
            
            if count > 0:
                # Show recent avatar changes
                recent = await conn.fetch("SELECT user_id, avatar, timestamp FROM avatar_history ORDER BY timestamp DESC LIMIT 5")
                print("\n📸 Recent avatar changes:")
                for record in recent:
                    print(f"   User {record['user_id']}: {record['avatar']} (timestamp: {record['timestamp']})")
        else:
            print("❌ avatar_history table does not exist")
            
        await conn.close()
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return
    
    print("\n" + "=" * 50)
    print("🎯 TROUBLESHOOTING STEPS:")
    print("1. Make sure you run '/avatarhistory enable' in Discord")
    print("2. Change your Discord avatar")
    print("3. Wait 10-15 seconds")
    print("4. Run '/avatarhistory view' in Discord")
    print("5. If still not working, check bot logs for errors")

if __name__ == "__main__":
    asyncio.run(debug_avatar_system())
